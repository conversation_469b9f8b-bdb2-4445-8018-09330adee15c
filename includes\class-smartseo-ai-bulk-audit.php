<?php
/**
 * Classe pour l'audit SEO en masse
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'audit SEO en masse
 */
class SmartSEO_AI_Bulk_Audit {

    /**
     * Instance de la classe
     *
     * @var SmartSEO_AI_Bulk_Audit
     */
    private static $instance = null;

    /**
     * Instance de la classe d'audit SEO
     *
     * @var SmartSEO_AI_SEO_Audit
     */
    private $seo_audit;

    /**
     * Résultats des audits
     *
     * @var array
     */
    private $audit_results = array();

    /**
     * Constructeur
     */
    public function __construct() {
        // Récupérer l'instance de la classe d'audit SEO
        $this->seo_audit = SmartSEO_AI_SEO_Audit::get_instance();

        // Ajouter le menu d'administration
        add_action( 'admin_menu', array( $this, 'add_bulk_audit_menu' ) );
        
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_smartseo_ai_run_bulk_audit', array( $this, 'ajax_run_bulk_audit' ) );
        add_action( 'wp_ajax_smartseo_ai_get_audit_results', array( $this, 'ajax_get_audit_results' ) );
        add_action( 'wp_ajax_smartseo_ai_auto_fix', array( $this, 'ajax_auto_fix' ) );
        add_action( 'wp_ajax_smartseo_ai_export_audit', array( $this, 'ajax_export_audit' ) );
    }

    /**
     * Ajoute le sous-menu pour l'audit SEO en masse
     */
    public function add_bulk_audit_menu() {
        add_submenu_page(
            'smartseo-ai',
            __( 'Audit SEO en masse', 'smartseo-ai' ),
            __( 'Audit SEO en masse', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai-bulk-audit',
            array( $this, 'render_bulk_audit_page' )
        );
    }

    /**
     * Affiche la page d'audit SEO en masse
     */
    public function render_bulk_audit_page() {
        // Inclure le template
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/bulk-seo-audit.php';
    }

    /**
     * Exécute l'audit SEO en masse via AJAX
     */
    public function ajax_run_bulk_audit() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les paramètres
        $post_types = isset( $_POST['post_types'] ) ? array_map( 'sanitize_text_field', $_POST['post_types'] ) : array( 'post', 'page' );
        $limit = isset( $_POST['limit'] ) ? intval( $_POST['limit'] ) : 10;
        $offset = isset( $_POST['offset'] ) ? intval( $_POST['offset'] ) : 0;

        // Récupérer les articles/pages à auditer
        $args = array(
            'post_type'      => $post_types,
            'post_status'    => 'publish',
            'posts_per_page' => $limit,
            'offset'         => $offset,
            'orderby'        => 'date',
            'order'          => 'DESC',
        );

        $query = new WP_Query( $args );
        $posts = $query->posts;
        $total_posts = $query->found_posts;

        // Initialiser les résultats
        $results = array();
        $processed = 0;

        // Auditer chaque article/page
        foreach ( $posts as $post ) {
            $audit_result = $this->seo_audit->run_audit( $post->ID );
            
            // Stocker les résultats
            $results[] = array(
                'post_id'    => $post->ID,
                'title'      => $post->post_title,
                'url'        => get_permalink( $post->ID ),
                'post_type'  => $post->post_type,
                'score'      => $audit_result['score'],
                'issues'     => $this->count_issues( $audit_result['report'] ),
                'report'     => $audit_result['report'],
                'timestamp'  => current_time( 'mysql' ),
            );

            $processed++;
        }

        // Stocker les résultats en option
        $this->save_audit_results( $results );

        // Envoyer les résultats
        wp_send_json_success( array(
            'processed'    => $processed,
            'total'        => $total_posts,
            'offset'       => $offset + $processed,
            'complete'     => ( $offset + $processed >= $total_posts ),
            'results'      => $results,
        ) );
    }

    /**
     * Récupère les résultats d'audit via AJAX
     */
    public function ajax_get_audit_results() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les paramètres
        $page = isset( $_POST['page'] ) ? intval( $_POST['page'] ) : 1;
        $per_page = isset( $_POST['per_page'] ) ? intval( $_POST['per_page'] ) : 20;
        $orderby = isset( $_POST['orderby'] ) ? sanitize_text_field( $_POST['orderby'] ) : 'score';
        $order = isset( $_POST['order'] ) ? sanitize_text_field( $_POST['order'] ) : 'asc';

        // Récupérer les résultats
        $results = $this->get_audit_results();

        // Trier les résultats
        usort( $results, function( $a, $b ) use ( $orderby, $order ) {
            if ( $orderby === 'score' ) {
                $result = $a['score'] - $b['score'];
            } elseif ( $orderby === 'issues' ) {
                $result = $a['issues'] - $b['issues'];
            } elseif ( $orderby === 'title' ) {
                $result = strcmp( $a['title'], $b['title'] );
            } elseif ( $orderby === 'post_type' ) {
                $result = strcmp( $a['post_type'], $b['post_type'] );
            } elseif ( $orderby === 'timestamp' ) {
                $result = strtotime( $a['timestamp'] ) - strtotime( $b['timestamp'] );
            } else {
                $result = 0;
            }

            return $order === 'asc' ? $result : -$result;
        } );

        // Paginer les résultats
        $total = count( $results );
        $offset = ( $page - 1 ) * $per_page;
        $results = array_slice( $results, $offset, $per_page );

        // Envoyer les résultats
        wp_send_json_success( array(
            'results'  => $results,
            'total'    => $total,
            'pages'    => ceil( $total / $per_page ),
            'page'     => $page,
            'per_page' => $per_page,
        ) );
    }

    /**
     * Corrige automatiquement les problèmes SEO via AJAX
     */
    public function ajax_auto_fix() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les paramètres
        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $issues = isset( $_POST['issues'] ) ? $_POST['issues'] : array();

        if ( empty( $post_id ) ) {
            wp_send_json_error( array( 'message' => __( 'ID d\'article invalide.', 'smartseo-ai' ) ) );
        }

        // Récupérer l'article
        $post = get_post( $post_id );
        if ( ! $post ) {
            wp_send_json_error( array( 'message' => __( 'Article introuvable.', 'smartseo-ai' ) ) );
        }

        // Initialiser les corrections
        $fixes = array();
        $fixed_count = 0;

        // Corriger les problèmes
        if ( in_array( 'meta_description', $issues, true ) ) {
            // Générer une meta description
            $content = $post->post_content;
            $title = $post->post_title;
            $excerpt = $post->post_excerpt;
            
            // Utiliser l'extrait s'il existe, sinon générer à partir du contenu
            if ( ! empty( $excerpt ) ) {
                $meta_description = wp_trim_words( $excerpt, 30, '...' );
            } else {
                $meta_description = wp_trim_words( strip_tags( $content ), 30, '...' );
            }
            
            // Enregistrer la meta description
            update_post_meta( $post_id, 'smartseo_ai_meta_description', $meta_description );
            
            // Si Yoast SEO est actif, enregistrer également la meta description dans Yoast
            if ( defined( 'WPSEO_VERSION' ) ) {
                update_post_meta( $post_id, '_yoast_wpseo_metadesc', $meta_description );
            }
            
            $fixes[] = __( 'Meta description générée et enregistrée.', 'smartseo-ai' );
            $fixed_count++;
        }

        if ( in_array( 'image_alt', $issues, true ) ) {
            // Ajouter des attributs alt aux images
            $content = $post->post_content;
            $title = $post->post_title;
            
            // Trouver toutes les images sans attribut alt
            preg_match_all( '/<img[^>]+>/i', $content, $img_tags );
            $updated_content = $content;
            
            foreach ( $img_tags[0] as $img_tag ) {
                // Vérifier si l'image a déjà un attribut alt
                if ( ! preg_match( '/alt=([\'"])(.*?)\1/i', $img_tag ) ) {
                    // Générer un attribut alt basé sur le titre de l'article
                    $alt_text = $title;
                    
                    // Ajouter l'attribut alt à l'image
                    $new_img_tag = str_replace( '>', ' alt="' . esc_attr( $alt_text ) . '">', $img_tag );
                    $updated_content = str_replace( $img_tag, $new_img_tag, $updated_content );
                }
            }
            
            // Mettre à jour le contenu de l'article
            if ( $updated_content !== $content ) {
                wp_update_post( array(
                    'ID' => $post_id,
                    'post_content' => $updated_content,
                ) );
                
                $fixes[] = __( 'Attributs alt ajoutés aux images.', 'smartseo-ai' );
                $fixed_count++;
            }
        }

        // Réexécuter l'audit pour obtenir les nouveaux résultats
        $audit_result = $this->seo_audit->run_audit( $post_id );
        
        // Mettre à jour les résultats stockés
        $this->update_audit_result( $post_id, $audit_result );

        // Envoyer les résultats
        wp_send_json_success( array(
            'fixed_count' => $fixed_count,
            'fixes' => $fixes,
            'new_score' => $audit_result['score'],
            'new_issues' => $this->count_issues( $audit_result['report'] ),
        ) );
    }

    /**
     * Exporte les résultats d'audit via AJAX
     */
    public function ajax_export_audit() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les paramètres
        $format = isset( $_POST['format'] ) ? sanitize_text_field( $_POST['format'] ) : 'csv';
        $ids = isset( $_POST['ids'] ) ? array_map( 'intval', $_POST['ids'] ) : array();

        // Récupérer les résultats
        $results = $this->get_audit_results();

        // Filtrer les résultats si des IDs sont spécifiés
        if ( ! empty( $ids ) ) {
            $results = array_filter( $results, function( $result ) use ( $ids ) {
                return in_array( $result['post_id'], $ids, true );
            } );
        }

        // Initialiser l'exportateur
        $exporter = new SmartSEO_AI_Audit_Exporter();

        // Exporter les résultats
        if ( $format === 'csv' ) {
            $export_data = $exporter->export_csv( $results );
        } else {
            $export_data = $exporter->export_pdf( $results );
        }

        // Envoyer les résultats
        wp_send_json_success( array(
            'export_data' => $export_data,
            'format' => $format,
        ) );
    }

    /**
     * Compte le nombre de problèmes dans un rapport
     *
     * @param array $report Rapport d'audit.
     * @return int Nombre de problèmes.
     */
    private function count_issues( $report ) {
        $issues_count = 0;

        if ( isset( $report['summary'] ) ) {
            $issues_count = $report['summary']['total_issues'];
        } else {
            // Compter manuellement les problèmes
            foreach ( $report['sections'] as $section ) {
                if ( isset( $section['issues'] ) ) {
                    $issues_count += count( $section['issues'] );
                }
            }
        }

        return $issues_count;
    }

    /**
     * Enregistre les résultats d'audit
     *
     * @param array $results Résultats d'audit.
     */
    private function save_audit_results( $results ) {
        // Récupérer les résultats existants
        $existing_results = $this->get_audit_results();

        // Fusionner les résultats
        foreach ( $results as $result ) {
            $existing_results = $this->update_audit_result( $result['post_id'], $result, $existing_results );
        }

        // Enregistrer les résultats
        update_option( 'smartseo_ai_audit_results', $existing_results );
    }

    /**
     * Met à jour un résultat d'audit
     *
     * @param int   $post_id ID de l'article.
     * @param array $result  Résultat d'audit.
     * @param array $results Résultats existants (facultatif).
     * @return array Résultats mis à jour.
     */
    private function update_audit_result( $post_id, $result, $results = null ) {
        // Récupérer les résultats existants si non fournis
        if ( $results === null ) {
            $results = $this->get_audit_results();
        }

        // Rechercher l'index du résultat existant
        $index = -1;
        foreach ( $results as $key => $existing_result ) {
            if ( $existing_result['post_id'] === $post_id ) {
                $index = $key;
                break;
            }
        }

        // Préparer le résultat
        $audit_result = array();
        if ( isset( $result['report'] ) ) {
            // Résultat complet de l'audit
            $audit_result = array(
                'post_id'    => $post_id,
                'title'      => get_the_title( $post_id ),
                'url'        => get_permalink( $post_id ),
                'post_type'  => get_post_type( $post_id ),
                'score'      => $result['score'],
                'issues'     => $this->count_issues( $result['report'] ),
                'report'     => $result['report'],
                'timestamp'  => current_time( 'mysql' ),
            );
        } else {
            // Résultat déjà formaté
            $audit_result = $result;
        }

        // Mettre à jour ou ajouter le résultat
        if ( $index >= 0 ) {
            $results[$index] = $audit_result;
        } else {
            $results[] = $audit_result;
        }

        return $results;
    }

    /**
     * Récupère les résultats d'audit
     *
     * @return array Résultats d'audit.
     */
    public function get_audit_results() {
        $results = get_option( 'smartseo_ai_audit_results', array() );
        return is_array( $results ) ? $results : array();
    }

    /**
     * Retourne l'instance unique de la classe
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}
