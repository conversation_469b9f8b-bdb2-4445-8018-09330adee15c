<?php
/**
 * Classe pour analyser les balises d'en-tête
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui analyse la hiérarchie des balises H1, H2, H3, etc.
 */
class SmartSEO_AI_Heading_Analyzer {

    /**
     * Constructeur
     */
    public function __construct() {
        // Rien à initialiser pour l'instant
    }

    /**
     * Analyse les balises d'en-tête d'un article
     *
     * @param int $post_id ID de l'article à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_post( $post_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return array(
                'status' => 'error',
                'message' => __( 'Article introuvable.', 'smartseo-ai' ),
            );
        }

        // Récupérer le contenu de l'article
        $content = $post->post_content;

        // Analyser les balises d'en-tête dans le contenu
        return $this->analyze_content_headings( $content );
    }

    /**
     * Analyse les balises d'en-tête d'une URL
     *
     * @param string $url URL à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_url( $url ) {
        // Récupérer le contenu de la page
        $response = wp_remote_get( $url );
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }

        $html = wp_remote_retrieve_body( $response );
        if ( empty( $html ) ) {
            return array(
                'status' => 'error',
                'message' => __( 'Impossible de récupérer le contenu de la page.', 'smartseo-ai' ),
            );
        }

        // Créer un objet DOMDocument
        $doc = new DOMDocument();
        @$doc->loadHTML( mb_convert_encoding( $html, 'HTML-ENTITIES', 'UTF-8' ) );
        $xpath = new DOMXPath( $doc );

        // Récupérer toutes les balises d'en-tête
        $headings = array();
        for ( $i = 1; $i <= 6; $i++ ) {
            $h_tags = $xpath->query( "//h{$i}" );
            foreach ( $h_tags as $tag ) {
                $headings[] = array(
                    'level' => $i,
                    'content' => $tag->nodeValue,
                );
            }
        }

        // Analyser la hiérarchie des balises d'en-tête
        return $this->analyze_headings_hierarchy( $headings );
    }

    /**
     * Analyse les balises d'en-tête dans le contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_content_headings( $content ) {
        // Créer un objet DOMDocument
        $doc = new DOMDocument();
        @$doc->loadHTML( mb_convert_encoding( $content, 'HTML-ENTITIES', 'UTF-8' ) );
        $xpath = new DOMXPath( $doc );

        // Récupérer toutes les balises d'en-tête
        $headings = array();
        for ( $i = 1; $i <= 6; $i++ ) {
            $h_tags = $xpath->query( "//h{$i}" );
            foreach ( $h_tags as $tag ) {
                $headings[] = array(
                    'level' => $i,
                    'content' => $tag->nodeValue,
                );
            }
        }

        // Analyser la hiérarchie des balises d'en-tête
        return $this->analyze_headings_hierarchy( $headings );
    }

    /**
     * Analyse la hiérarchie des balises d'en-tête
     *
     * @param array $headings Balises d'en-tête à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_headings_hierarchy( $headings ) {
        $results = array(
            'status' => 'success',
            'headings' => $headings,
            'counts' => array(
                'h1' => 0,
                'h2' => 0,
                'h3' => 0,
                'h4' => 0,
                'h5' => 0,
                'h6' => 0,
            ),
            'issues' => array(),
            'recommendations' => array(),
            'score' => 0,
        );

        // Compter les balises d'en-tête par niveau
        foreach ( $headings as $heading ) {
            $results['counts']["h{$heading['level']}"]++;
        }

        // Vérifier s'il y a une balise H1
        if ( $results['counts']['h1'] === 0 ) {
            $results['issues'][] = __( 'Aucune balise H1 n\'a été trouvée.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Ajoutez une balise H1 qui contient le titre principal de la page.', 'smartseo-ai' );
        } elseif ( $results['counts']['h1'] > 1 ) {
            $results['issues'][] = sprintf(
                __( '%d balises H1 ont été trouvées. Il ne devrait y en avoir qu\'une seule.', 'smartseo-ai' ),
                $results['counts']['h1']
            );
            $results['recommendations'][] = __( 'Gardez une seule balise H1 par page.', 'smartseo-ai' );
        }

        // Vérifier la hiérarchie des balises d'en-tête
        $previous_level = 0;
        $hierarchy_issues = 0;

        foreach ( $headings as $heading ) {
            $level = $heading['level'];

            // Vérifier si le niveau est supérieur de plus de 1 par rapport au niveau précédent
            if ( $previous_level > 0 && $level > $previous_level + 1 ) {
                $hierarchy_issues++;
            }

            $previous_level = $level;
        }

        if ( $hierarchy_issues > 0 ) {
            $results['issues'][] = sprintf(
                _n(
                    '%d problème de hiérarchie a été détecté.',
                    '%d problèmes de hiérarchie ont été détectés.',
                    $hierarchy_issues,
                    'smartseo-ai'
                ),
                $hierarchy_issues
            );
            $results['recommendations'][] = __( 'Assurez-vous que la hiérarchie des balises d\'en-tête est correcte (H1 > H2 > H3, etc.).', 'smartseo-ai' );
        }

        // Calculer le score
        $score = 100;

        // Pénalité pour l'absence de H1 ou plusieurs H1
        if ( $results['counts']['h1'] === 0 ) {
            $score -= 30;
        } elseif ( $results['counts']['h1'] > 1 ) {
            $score -= 20;
        }

        // Pénalité pour les problèmes de hiérarchie
        if ( $hierarchy_issues > 0 ) {
            $score -= min( 50, $hierarchy_issues * 10 );
        }

        $results['score'] = max( 0, $score );

        return $results;
    }
}
