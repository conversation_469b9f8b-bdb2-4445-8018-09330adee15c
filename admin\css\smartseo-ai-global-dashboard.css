/**
 * Styles pour le Dashboard SEO Global
 *
 * @package SmartSEO_AI
 */

/* Conteneur principal */
.smartseo-ai-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
    overflow: hidden;
}

/* En-tête */
.smartseo-ai-header {
    background-color: #2271b1;
    color: #fff;
    padding: 20px;
    position: relative;
}

.smartseo-ai-header h1 {
    color: #fff;
    font-size: 24px;
    margin: 0;
}

.smartseo-ai-version {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
    padding: 2px 6px;
    position: absolute;
    right: 20px;
    top: 20px;
}

/* Contenu */
.smartseo-ai-content {
    padding: 20px;
    position: relative;
}

/* Loader */
.smartseo-ai-loader-container {
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    height: 100%;
    justify-content: center;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 100;
}

.smartseo-ai-loader {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.smartseo-ai-loader-spinner {
    animation: smartseo-ai-spin 1.5s linear infinite;
    border: 4px solid #f3f3f3;
    border-radius: 50%;
    border-top: 4px solid #2271b1;
    height: 40px;
    width: 40px;
}

.smartseo-ai-loader-text {
    color: #2271b1;
    font-size: 16px;
    font-weight: 600;
    margin-top: 10px;
}

@keyframes smartseo-ai-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Statistiques globales */
.smartseo-ai-global-stats {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    margin-bottom: 30px;
}

.smartseo-ai-stat-card {
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 20px;
    text-align: center;
    transition: transform 0.2s ease-in-out;
}

.smartseo-ai-stat-card:hover {
    transform: translateY(-5px);
}

.smartseo-ai-stat-card h3 {
    color: #23282d;
    font-size: 16px;
    margin: 0 0 10px;
}

.smartseo-ai-stat-card .stat-value {
    color: #2271b1;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 5px;
}

.smartseo-ai-stat-card .stat-description {
    color: #646970;
    font-size: 12px;
}

/* Classes de score */
.score-excellent {
    color: #46b450;
}

.score-good {
    color: #00a0d2;
}

.score-average {
    color: #ffb900;
}

.score-poor {
    color: #dc3232;
}

/* Graphiques */
.smartseo-ai-charts-container {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    margin-bottom: 30px;
}

.smartseo-ai-chart-card {
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 20px;
}

.smartseo-ai-chart-card h3 {
    color: #23282d;
    font-size: 16px;
    margin: 0 0 15px;
}

.smartseo-ai-chart-wrapper {
    height: 300px;
    position: relative;
}

/* Filtres */
.smartseo-ai-filters-container {
    background-color: #f9f9f9;
    border-radius: 8px;
    margin-bottom: 30px;
    padding: 20px;
}

.smartseo-ai-filters-container h2 {
    color: #23282d;
    font-size: 18px;
    margin: 0 0 15px;
}

.smartseo-ai-filters {
    display: grid;
    gap: 15px;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    margin-bottom: 20px;
}

.smartseo-ai-filter-group {
    display: flex;
    flex-direction: column;
}

.smartseo-ai-filter-group label {
    color: #646970;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.smartseo-ai-filter {
    border: 1px solid #dcdcde;
    border-radius: 4px;
    padding: 6px 8px;
}

.smartseo-ai-range-slider {
    display: flex;
    flex-direction: column;
}

.range-slider-values {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.range-slider-container {
    display: flex;
    position: relative;
}

.range-slider-container input[type="range"] {
    margin: 0;
    position: absolute;
    width: 100%;
}

.range-slider-container input[type="range"]:nth-child(1) {
    z-index: 2;
}

.smartseo-ai-date-range {
    display: flex;
    align-items: center;
    gap: 5px;
}

.smartseo-ai-date-range input {
    flex: 1;
}

.smartseo-ai-filter-actions {
    display: flex;
    gap: 10px;
    grid-column: 1 / -1;
    justify-content: flex-start;
    margin-top: 10px;
}

.smartseo-ai-export-container {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Tableau */
.smartseo-ai-table-container {
    margin-bottom: 20px;
    overflow-x: auto;
}

.smartseo-ai-posts-table {
    border-collapse: collapse;
    width: 100%;
}

.smartseo-ai-posts-table th,
.smartseo-ai-posts-table td {
    border-bottom: 1px solid #e5e5e5;
    padding: 12px 15px;
    text-align: left;
}

.smartseo-ai-posts-table th {
    background-color: #f9f9f9;
    color: #23282d;
    font-weight: 600;
}

.smartseo-ai-posts-table tr:hover {
    background-color: #f9f9f9;
}

.smartseo-ai-posts-table .column-title {
    width: 25%;
}

.smartseo-ai-posts-table .column-type {
    width: 10%;
}

.smartseo-ai-posts-table .column-score {
    width: 10%;
}

.smartseo-ai-posts-table .column-status {
    width: 15%;
}

.smartseo-ai-posts-table .column-errors {
    width: 15%;
}

.smartseo-ai-posts-table .column-date {
    width: 10%;
}

.smartseo-ai-posts-table .column-actions {
    width: 15%;
}

.smartseo-ai-score-indicator {
    border-radius: 4px;
    display: inline-block;
    font-weight: 600;
    padding: 4px 8px;
    text-align: center;
}

.smartseo-ai-score-excellent {
    background-color: rgba(70, 180, 80, 0.1);
    color: #46b450;
}

.smartseo-ai-score-good {
    background-color: rgba(0, 160, 210, 0.1);
    color: #00a0d2;
}

.smartseo-ai-score-average {
    background-color: rgba(255, 185, 0, 0.1);
    color: #ffb900;
}

.smartseo-ai-score-poor {
    background-color: rgba(220, 50, 50, 0.1);
    color: #dc3232;
}

.smartseo-ai-status-indicator {
    border-radius: 4px;
    display: inline-block;
    font-weight: 600;
    padding: 4px 8px;
    text-align: center;
}

.smartseo-ai-status-optimized {
    background-color: rgba(70, 180, 80, 0.1);
    color: #46b450;
}

.smartseo-ai-status-partially {
    background-color: rgba(255, 185, 0, 0.1);
    color: #ffb900;
}

.smartseo-ai-status-not_optimized {
    background-color: rgba(220, 50, 50, 0.1);
    color: #dc3232;
}

.smartseo-ai-no-errors {
    color: #46b450;
    display: flex;
    align-items: center;
    gap: 5px;
}

.smartseo-ai-show-errors {
    display: flex;
    align-items: center;
    gap: 5px;
}

.smartseo-ai-no-data {
    color: #646970;
    text-align: center;
}

/* Pagination */
.smartseo-ai-pagination {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.smartseo-ai-pagination-info {
    color: #646970;
    font-size: 14px;
}

.smartseo-ai-pagination-controls {
    align-items: center;
    display: flex;
    gap: 5px;
}

.smartseo-ai-pagination-controls button {
    padding: 0;
    height: 30px;
    width: 30px;
}

.smartseo-ai-pagination-controls button .dashicons {
    font-size: 16px;
    height: 16px;
    width: 16px;
}

.smartseo-ai-pagination-controls #pagination-current {
    margin: 0 10px;
}

.smartseo-ai-pagination-per-page {
    align-items: center;
    display: flex;
    gap: 5px;
}

.smartseo-ai-pagination-per-page label {
    color: #646970;
    font-size: 14px;
}

.smartseo-ai-pagination-per-page select {
    width: 70px;
}

/* Notifications */
.smartseo-ai-notifications-container {
    bottom: 20px;
    position: fixed;
    right: 20px;
    width: 300px;
    z-index: 9999;
}

.smartseo-ai-notification {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    margin-bottom: 10px;
    opacity: 0;
    padding: 15px;
    transform: translateX(100%);
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
}

.smartseo-ai-notification-visible {
    opacity: 1;
    transform: translateX(0);
}

.smartseo-ai-notification-success {
    border-left: 4px solid #46b450;
}

.smartseo-ai-notification-error {
    border-left: 4px solid #dc3232;
}

.smartseo-ai-notification-warning {
    border-left: 4px solid #ffb900;
}

.smartseo-ai-notification-info {
    border-left: 4px solid #00a0d2;
}

.smartseo-ai-notification-icon {
    margin-right: 10px;
}

.smartseo-ai-notification-icon .dashicons {
    color: #646970;
    font-size: 20px;
    height: 20px;
    width: 20px;
}

.smartseo-ai-notification-success .smartseo-ai-notification-icon .dashicons {
    color: #46b450;
}

.smartseo-ai-notification-error .smartseo-ai-notification-icon .dashicons {
    color: #dc3232;
}

.smartseo-ai-notification-warning .smartseo-ai-notification-icon .dashicons {
    color: #ffb900;
}

.smartseo-ai-notification-info .smartseo-ai-notification-icon .dashicons {
    color: #00a0d2;
}

.smartseo-ai-notification-content {
    flex: 1;
}

.smartseo-ai-notification-message {
    color: #23282d;
    font-size: 14px;
    margin: 0;
}

.smartseo-ai-notification-close {
    background: none;
    border: none;
    color: #646970;
    cursor: pointer;
    padding: 0;
}

.smartseo-ai-notification-close:hover {
    color: #23282d;
}

/* Modales */
.smartseo-ai-modal-overlay {
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    height: 100%;
    justify-content: center;
    left: 0;
    opacity: 0;
    position: fixed;
    top: 0;
    transition: opacity 0.3s ease-out;
    width: 100%;
    z-index: 9999;
}

.smartseo-ai-modal-visible {
    opacity: 1;
}

.smartseo-ai-modal {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    max-height: 80vh;
    max-width: 600px;
    overflow: hidden;
    width: 100%;
}

.smartseo-ai-modal-header {
    align-items: center;
    background-color: #2271b1;
    color: #fff;
    display: flex;
    justify-content: space-between;
    padding: 15px 20px;
}

.smartseo-ai-modal-header h2 {
    color: #fff;
    font-size: 18px;
    margin: 0;
}

.smartseo-ai-modal-close {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    padding: 0;
}

.smartseo-ai-modal-content {
    max-height: 60vh;
    overflow-y: auto;
    padding: 20px;
}

.smartseo-ai-modal-footer {
    align-items: center;
    background-color: #f9f9f9;
    border-top: 1px solid #e5e5e5;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px 20px;
}

.smartseo-ai-errors-list {
    margin: 0;
    padding: 0 0 0 20px;
}

.smartseo-ai-errors-list li {
    margin-bottom: 10px;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .smartseo-ai-charts-container {
        grid-template-columns: 1fr;
    }
    
    .smartseo-ai-filters {
        grid-template-columns: 1fr;
    }
    
    .smartseo-ai-pagination {
        flex-direction: column;
        gap: 10px;
    }
}
