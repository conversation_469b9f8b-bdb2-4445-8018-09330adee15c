<?php
/**
 * Template pour le rapport d'audit SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<script id="audit-report-template" type="text/x-handlebars-template">
    <div class="smartseo-ai-audit-summary">
        <h2><?php esc_html_e( 'Résumé de l\'audit SEO', 'smartseo-ai' ); ?></h2>

        <div class="smartseo-ai-score-container">
            <div class="smartseo-ai-score-circle smartseo-ai-score-{{scoreClass score}}">
                <div class="smartseo-ai-score-value">{{score}}</div>
                <div class="smartseo-ai-score-label"><?php esc_html_e( 'Score', 'smartseo-ai' ); ?></div>
            </div>

            <div class="smartseo-ai-score-details">
                <div class="smartseo-ai-score-detail">
                    <span class="dashicons dashicons-warning"></span>
                    <span class="smartseo-ai-score-detail-value">{{summary.total_issues}}</span>
                    <span class="smartseo-ai-score-detail-label"><?php esc_html_e( 'Problèmes détectés', 'smartseo-ai' ); ?></span>
                </div>

                <div class="smartseo-ai-score-detail smartseo-ai-score-critical">
                    <span class="dashicons dashicons-no-alt"></span>
                    <span class="smartseo-ai-score-detail-value">{{summary.critical_issues}}</span>
                    <span class="smartseo-ai-score-detail-label"><?php esc_html_e( 'Critiques', 'smartseo-ai' ); ?></span>
                </div>

                <div class="smartseo-ai-score-detail smartseo-ai-score-warning">
                    <span class="dashicons dashicons-warning"></span>
                    <span class="smartseo-ai-score-detail-value">{{summary.important_issues}}</span>
                    <span class="smartseo-ai-score-detail-label"><?php esc_html_e( 'Importants', 'smartseo-ai' ); ?></span>
                </div>

                <div class="smartseo-ai-score-detail smartseo-ai-score-info">
                    <span class="dashicons dashicons-info"></span>
                    <span class="smartseo-ai-score-detail-value">{{summary.minor_issues}}</span>
                    <span class="smartseo-ai-score-detail-label"><?php esc_html_e( 'Mineurs', 'smartseo-ai' ); ?></span>
                </div>
            </div>
        </div>
    </div>

    <div class="smartseo-ai-audit-sections">
        {{#each sections}}
            <div class="smartseo-ai-accordion">
                <div class="smartseo-ai-accordion-header smartseo-ai-status-{{statusClass status}}">
                    <span class="dashicons dashicons-{{statusIcon status}}"></span>
                    <h3>{{title}}</h3>
                    <div class="smartseo-ai-section-score smartseo-ai-score-{{scoreClass score}}">{{score}}</div>
                    <span class="smartseo-ai-accordion-toggle dashicons dashicons-arrow-down-alt2"></span>
                </div>

                <div class="smartseo-ai-accordion-content">
                    {{#if message}}
                        <div class="smartseo-ai-notice smartseo-ai-notice-error">
                            <p>{{message}}</p>
                        </div>
                    {{else}}
                        {{#if issues.length}}
                            <div class="smartseo-ai-issues-container">
                                <h4><?php esc_html_e( 'Problèmes détectés', 'smartseo-ai' ); ?></h4>
                                <ul class="smartseo-ai-issues-list">
                                    {{#each issues}}
                                        <li><span class="dashicons dashicons-warning"></span> {{this}}</li>
                                    {{/each}}
                                </ul>
                            </div>
                        {{/if}}

                        {{#if recommendations.length}}
                            <div class="smartseo-ai-recommendations-container">
                                <h4><?php esc_html_e( 'Recommandations', 'smartseo-ai' ); ?></h4>
                                <ul class="smartseo-ai-recommendations-list">
                                    {{#each recommendations}}
                                        <li><span class="dashicons dashicons-lightbulb"></span> {{this}}</li>
                                    {{/each}}
                                </ul>
                            </div>
                        {{/if}}

                        <div class="smartseo-ai-details-container">
                            <h4><?php esc_html_e( 'Détails', 'smartseo-ai' ); ?></h4>

                            {{#if details.title}}
                                <div class="smartseo-ai-detail-item">
                                    <div class="smartseo-ai-detail-label"><?php esc_html_e( 'Titre', 'smartseo-ai' ); ?></div>
                                    <div class="smartseo-ai-detail-value">{{details.title.content}}</div>
                                    <div class="smartseo-ai-detail-meta"><?php esc_html_e( 'Longueur', 'smartseo-ai' ); ?>: {{details.title.length}} <?php esc_html_e( 'caractères', 'smartseo-ai' ); ?></div>
                                </div>
                            {{/if}}

                            {{#if details.meta_description}}
                                <div class="smartseo-ai-detail-item">
                                    <div class="smartseo-ai-detail-label"><?php esc_html_e( 'Meta Description', 'smartseo-ai' ); ?></div>
                                    <div class="smartseo-ai-detail-value">{{details.meta_description.content}}</div>
                                    <div class="smartseo-ai-detail-meta"><?php esc_html_e( 'Longueur', 'smartseo-ai' ); ?>: {{details.meta_description.length}} <?php esc_html_e( 'caractères', 'smartseo-ai' ); ?></div>
                                </div>
                            {{/if}}

                            {{#if details.total_images}}
                                <div class="smartseo-ai-detail-item">
                                    <div class="smartseo-ai-detail-label"><?php esc_html_e( 'Images', 'smartseo-ai' ); ?></div>
                                    <div class="smartseo-ai-detail-value">
                                        <?php esc_html_e( 'Total', 'smartseo-ai' ); ?>: {{details.total_images}}<br>
                                        <?php esc_html_e( 'Avec attribut alt', 'smartseo-ai' ); ?>: {{details.images_with_alt}}<br>
                                        <?php esc_html_e( 'Sans attribut alt', 'smartseo-ai' ); ?>: {{details.images_without_alt}}<br>
                                        <?php esc_html_e( 'Avec attribut alt vide', 'smartseo-ai' ); ?>: {{details.images_with_empty_alt}}
                                    </div>
                                </div>
                            {{/if}}

                            {{#if details.counts}}
                                <div class="smartseo-ai-detail-item">
                                    <div class="smartseo-ai-detail-label"><?php esc_html_e( 'Balises d\'en-tête', 'smartseo-ai' ); ?></div>
                                    <div class="smartseo-ai-detail-value">
                                        H1: {{details.counts.h1}}<br>
                                        H2: {{details.counts.h2}}<br>
                                        H3: {{details.counts.h3}}<br>
                                        H4: {{details.counts.h4}}<br>
                                        H5: {{details.counts.h5}}<br>
                                        H6: {{details.counts.h6}}
                                    </div>
                                </div>
                            {{/if}}

                            {{#if details.url}}
                                <div class="smartseo-ai-detail-item">
                                    <div class="smartseo-ai-detail-label"><?php esc_html_e( 'URL', 'smartseo-ai' ); ?></div>
                                    <div class="smartseo-ai-detail-value">{{details.url}}</div>
                                    {{#if details.slug}}
                                        <div class="smartseo-ai-detail-meta"><?php esc_html_e( 'Slug', 'smartseo-ai' ); ?>: {{details.slug}}</div>
                                    {{/if}}
                                </div>
                            {{/if}}

                            {{#if details.total_links}}
                                <div class="smartseo-ai-detail-item">
                                    <div class="smartseo-ai-detail-label"><?php esc_html_e( 'Liens', 'smartseo-ai' ); ?></div>
                                    <div class="smartseo-ai-detail-value">
                                        <?php esc_html_e( 'Total', 'smartseo-ai' ); ?>: {{details.total_links}}<br>
                                        <?php esc_html_e( 'Internes', 'smartseo-ai' ); ?>: {{details.internal_links}}<br>
                                        <?php esc_html_e( 'Externes', 'smartseo-ai' ); ?>: {{details.external_links}}<br>
                                        <?php esc_html_e( 'Cassés', 'smartseo-ai' ); ?>: {{details.broken_links}}<br>
                                        <?php esc_html_e( 'Nofollow', 'smartseo-ai' ); ?>: {{details.nofollow_links}}
                                    </div>
                                </div>
                            {{/if}}

                            {{#if details.focus_keyword}}
                                <div class="smartseo-ai-detail-item">
                                    <div class="smartseo-ai-detail-label"><?php esc_html_e( 'Mot-clé principal', 'smartseo-ai' ); ?></div>
                                    <div class="smartseo-ai-detail-value">{{details.focus_keyword}}</div>
                                    <div class="smartseo-ai-detail-meta">
                                        <?php esc_html_e( 'Densité', 'smartseo-ai' ); ?>: {{details.keyword_density}}%<br>
                                        <?php esc_html_e( 'Occurrences', 'smartseo-ai' ); ?>: {{details.keyword_count}}<br>
                                        <?php esc_html_e( 'Total de mots', 'smartseo-ai' ); ?>: {{details.total_words}}
                                    </div>
                                </div>

                                {{#if details.top_keywords}}
                                    <div class="smartseo-ai-detail-item">
                                        <div class="smartseo-ai-detail-label"><?php esc_html_e( 'Mots-clés les plus fréquents', 'smartseo-ai' ); ?></div>
                                        <div class="smartseo-ai-detail-value">
                                            <ul class="smartseo-ai-keywords-list">
                                                {{#each details.top_keywords}}
                                                    <li>{{@key}}: {{this}}</li>
                                                {{/each}}
                                            </ul>
                                        </div>
                                    </div>
                                {{/if}}
                            {{/if}}

                            {{#if details.robots_txt}}
                                <div class="smartseo-ai-detail-item">
                                    <div class="smartseo-ai-detail-label"><?php esc_html_e( 'Robots.txt', 'smartseo-ai' ); ?></div>
                                    <div class="smartseo-ai-detail-value">
                                        <?php esc_html_e( 'Existe', 'smartseo-ai' ); ?>: {{#if details.robots_txt.exists}}<?php esc_html_e( 'Oui', 'smartseo-ai' ); ?>{{else}}<?php esc_html_e( 'Non', 'smartseo-ai' ); ?>{{/if}}<br>
                                        {{#if details.robots_txt.has_sitemap_reference}}
                                            <?php esc_html_e( 'Référence au sitemap', 'smartseo-ai' ); ?>: <?php esc_html_e( 'Oui', 'smartseo-ai' ); ?>
                                        {{/if}}
                                    </div>
                                </div>
                            {{/if}}

                            {{#if details.sitemap}}
                                <div class="smartseo-ai-detail-item">
                                    <div class="smartseo-ai-detail-label"><?php esc_html_e( 'Sitemap', 'smartseo-ai' ); ?></div>
                                    <div class="smartseo-ai-detail-value">
                                        <?php esc_html_e( 'Existe', 'smartseo-ai' ); ?>: {{#if details.sitemap.exists}}<?php esc_html_e( 'Oui', 'smartseo-ai' ); ?>{{else}}<?php esc_html_e( 'Non', 'smartseo-ai' ); ?>{{/if}}<br>
                                        {{#if details.sitemap.exists}}
                                            <?php esc_html_e( 'URL', 'smartseo-ai' ); ?>: {{details.sitemap.url}}<br>
                                            <?php esc_html_e( 'Valide', 'smartseo-ai' ); ?>: {{#if details.sitemap.valid}}<?php esc_html_e( 'Oui', 'smartseo-ai' ); ?>{{else}}<?php esc_html_e( 'Non', 'smartseo-ai' ); ?>{{/if}}<br>
                                            {{#if details.sitemap.urls_count}}
                                                <?php esc_html_e( 'Nombre d\'URLs', 'smartseo-ai' ); ?>: {{details.sitemap.urls_count}}
                                            {{/if}}
                                        {{/if}}
                                    </div>
                                </div>
                            {{/if}}

                            {{#if details.has_schema}}
                                <div class="smartseo-ai-detail-item">
                                    <div class="smartseo-ai-detail-label"><?php esc_html_e( 'Données structurées', 'smartseo-ai' ); ?></div>
                                    <div class="smartseo-ai-detail-value">
                                        <?php esc_html_e( 'Présentes', 'smartseo-ai' ); ?>: <?php esc_html_e( 'Oui', 'smartseo-ai' ); ?><br>
                                        <?php esc_html_e( 'Nombre', 'smartseo-ai' ); ?>: {{details.schema_count}}<br>
                                        <?php esc_html_e( 'Types', 'smartseo-ai' ); ?>: {{#if details.schema_types}}{{joinArray details.schema_types ", "}}{{else}}<?php esc_html_e( 'Non spécifié', 'smartseo-ai' ); ?>{{/if}}
                                    </div>
                                </div>
                            {{/if}}
                        </div>
                    {{/if}}
                </div>
            </div>
        {{/each}}
    </div>

    <div class="smartseo-ai-audit-actions">
        <button id="smartseo-ai-print-report" class="button button-secondary">
            <span class="dashicons dashicons-printer"></span>
            <?php esc_html_e( 'Imprimer le rapport', 'smartseo-ai' ); ?>
        </button>

        <button id="smartseo-ai-export-report" class="button button-secondary">
            <span class="dashicons dashicons-download"></span>
            <?php esc_html_e( 'Exporter en PDF', 'smartseo-ai' ); ?>
        </button>
    </div>
</script>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Impression du rapport
    $(document).on('click', '#smartseo-ai-print-report', function() {
        window.print();
    });

    // Export en PDF (nécessite une bibliothèque comme jsPDF)
    $(document).on('click', '#smartseo-ai-export-report', function() {
        alert('<?php esc_attr_e( 'Fonctionnalité d\'export en PDF à implémenter.', 'smartseo-ai' ); ?>');
    });
});
</script>
