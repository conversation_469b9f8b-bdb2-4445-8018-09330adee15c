<?php
/**
 * Classe pour analyser les fichiers robots.txt et sitemap.xml
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui analyse les fichiers robots.txt et sitemap.xml
 */
class SmartSEO_AI_Robots_Sitemap_Analyzer {

    /**
     * Constructeur
     */
    public function __construct() {
        // Rien à initialiser pour l'instant
    }

    /**
     * Analyse les fichiers robots.txt et sitemap.xml du site
     *
     * @return array Résultats de l'analyse.
     */
    public function analyze_site() {
        $results = array(
            'status' => 'success',
            'robots_txt' => array(
                'exists' => false,
                'content' => '',
                'issues' => array(),
                'recommendations' => array(),
            ),
            'sitemap' => array(
                'exists' => false,
                'url' => '',
                'valid' => false,
                'issues' => array(),
                'recommendations' => array(),
            ),
            'score' => 0,
        );

        // Analyser le fichier robots.txt
        $robots_results = $this->analyze_robots_txt();
        $results['robots_txt'] = $robots_results;

        // Analyser le fichier sitemap.xml
        $sitemap_results = $this->analyze_sitemap();
        $results['sitemap'] = $sitemap_results;

        // Calculer le score global
        $score = 0;
        if ( $robots_results['exists'] ) {
            $score += 50;
            if ( $robots_results['has_sitemap_reference'] ) {
                $score += 10;
            }
        }

        if ( $sitemap_results['exists'] ) {
            $score += 30;
            if ( $sitemap_results['valid'] ) {
                $score += 10;
            }
        }

        $results['score'] = $score;

        return $results;
    }

    /**
     * Analyse le fichier robots.txt
     *
     * @return array Résultats de l'analyse.
     */
    private function analyze_robots_txt() {
        $results = array(
            'exists' => false,
            'content' => '',
            'has_sitemap_reference' => false,
            'issues' => array(),
            'recommendations' => array(),
        );

        // Vérifier si le fichier robots.txt existe
        $robots_url = site_url( '/robots.txt' );
        $response = wp_remote_get( $robots_url );

        if ( is_wp_error( $response ) ) {
            $results['issues'][] = __( 'Impossible d\'accéder au fichier robots.txt.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Vérifiez que votre site est accessible.', 'smartseo-ai' );
            return $results;
        }

        $response_code = wp_remote_retrieve_response_code( $response );
        if ( $response_code !== 200 ) {
            $results['issues'][] = __( 'Le fichier robots.txt n\'existe pas.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Créez un fichier robots.txt pour indiquer aux moteurs de recherche quelles parties de votre site doivent être explorées.', 'smartseo-ai' );
            return $results;
        }

        $results['exists'] = true;
        $results['content'] = wp_remote_retrieve_body( $response );

        // Vérifier si le fichier robots.txt fait référence à un sitemap
        if ( strpos( $results['content'], 'Sitemap:' ) !== false ) {
            $results['has_sitemap_reference'] = true;
        } else {
            $results['issues'][] = __( 'Le fichier robots.txt ne fait pas référence à un sitemap.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Ajoutez une référence à votre sitemap dans le fichier robots.txt.', 'smartseo-ai' );
        }

        // Vérifier si le fichier robots.txt bloque des ressources importantes
        $blocked_resources = array(
            '/wp-includes/',
            '/wp-content/uploads/',
            '/images/',
            '/css/',
            '/js/',
        );

        foreach ( $blocked_resources as $resource ) {
            if ( preg_match( '/Disallow:\s*' . preg_quote( $resource, '/' ) . '/i', $results['content'] ) ) {
                $results['issues'][] = sprintf(
                    __( 'Le fichier robots.txt bloque l\'accès à "%s".', 'smartseo-ai' ),
                    $resource
                );
                $results['recommendations'][] = sprintf(
                    __( 'Vérifiez si le blocage de "%s" est intentionnel.', 'smartseo-ai' ),
                    $resource
                );
            }
        }

        return $results;
    }

    /**
     * Analyse le fichier sitemap.xml
     *
     * @return array Résultats de l'analyse.
     */
    private function analyze_sitemap() {
        $results = array(
            'exists' => false,
            'url' => '',
            'valid' => false,
            'urls_count' => 0,
            'issues' => array(),
            'recommendations' => array(),
        );

        // Vérifier si le fichier sitemap.xml existe
        $sitemap_url = site_url( '/sitemap.xml' );
        $results['url'] = $sitemap_url;
        $response = wp_remote_get( $sitemap_url );

        if ( is_wp_error( $response ) ) {
            $results['issues'][] = __( 'Impossible d\'accéder au fichier sitemap.xml.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Vérifiez que votre site est accessible.', 'smartseo-ai' );
            return $results;
        }

        $response_code = wp_remote_retrieve_response_code( $response );
        if ( $response_code !== 200 ) {
            $results['issues'][] = __( 'Le fichier sitemap.xml n\'existe pas.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Créez un fichier sitemap.xml pour aider les moteurs de recherche à indexer votre site.', 'smartseo-ai' );
            return $results;
        }

        $results['exists'] = true;
        $sitemap_content = wp_remote_retrieve_body( $response );

        // Vérifier si le fichier sitemap.xml est valide
        libxml_use_internal_errors( true );
        $xml = simplexml_load_string( $sitemap_content );
        if ( $xml === false ) {
            $results['issues'][] = __( 'Le fichier sitemap.xml n\'est pas un XML valide.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Corrigez les erreurs de syntaxe dans votre fichier sitemap.xml.', 'smartseo-ai' );
            return $results;
        }

        $results['valid'] = true;

        // Compter le nombre d'URLs dans le sitemap
        $namespaces = $xml->getNamespaces( true );
        $urls_count = 0;

        // Vérifier s'il s'agit d'un sitemap standard ou d'un index de sitemaps
        if ( $xml->getName() === 'urlset' ) {
            // Sitemap standard
            $urls_count = count( $xml->url );
        } elseif ( $xml->getName() === 'sitemapindex' ) {
            // Index de sitemaps
            $urls_count = count( $xml->sitemap );
            $results['is_index'] = true;
        }

        $results['urls_count'] = $urls_count;

        if ( $urls_count === 0 ) {
            $results['issues'][] = __( 'Le fichier sitemap.xml ne contient aucune URL.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Assurez-vous que votre sitemap contient toutes les URLs importantes de votre site.', 'smartseo-ai' );
        }

        return $results;
    }
}
