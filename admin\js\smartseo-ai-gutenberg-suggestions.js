/**
 * Script pour l'intégration des suggestions SEO dans l'éditeur Gutenberg
 */

(function(wp) {
    const { __ } = wp.i18n;
    const { registerPlugin } = wp.plugins;
    const { PluginSidebar, PluginSidebarMoreMenuItem } = wp.editPost;
    const { PanelBody, Button, Spinner, Notice } = wp.components;
    const { useState, useEffect } = wp.element;
    const { createElement, Fragment } = wp.element;

    /**
     * Composant de suggestions SEO
     */
    const SmartSEOAISuggestions = function() {
        // États
        const [isLoading, setIsLoading] = useState(false);
        const [suggestions, setSuggestions] = useState([]);
        const [error, setError] = useState(null);

        /**
         * Récupère les suggestions SEO
         */
        const fetchSuggestions = function() {
            setIsLoading(true);
            setError(null);

            // Récupérer l'ID de l'article
            const postId = wp.data.select('core/editor').getCurrentPostId();
            
            // Récupérer le contenu de l'article
            const content = wp.data.select('core/editor').getEditedPostContent();
            
            // Récupérer le titre de l'article
            const title = wp.data.select('core/editor').getEditedPostAttribute('title');

            // Appeler l'API pour récupérer les suggestions
            jQuery.ajax({
                url: smartseoAiSuggestions.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartseo_ai_get_suggestions',
                    nonce: smartseoAiSuggestions.nonce,
                    post_id: postId,
                    content: content,
                    title: title
                },
                success: function(response) {
                    setIsLoading(false);
                    
                    if (response.success) {
                        setSuggestions(response.data);
                    } else {
                        setError(response.data.message || smartseoAiSuggestions.i18n.error);
                    }
                },
                error: function() {
                    setIsLoading(false);
                    setError(smartseoAiSuggestions.i18n.error);
                }
            });
        };

        /**
         * Applique une suggestion
         * @param {string} content Contenu à appliquer
         * @param {string} type    Type de suggestion
         */
        const applySuggestion = function(content, type) {
            // Récupérer le dispatcher
            const { editPost } = wp.data.dispatch('core/editor');
            
            // Appliquer la suggestion en fonction du type
            if (type === 'title') {
                editPost({ title: content });
            } else if (type === 'meta_description') {
                editPost({ meta: { smartseo_ai_meta_description: content } });
            } else if (type === 'content') {
                // Insérer le contenu à la position du curseur
                const { insertBlocks } = wp.data.dispatch('core/block-editor');
                insertBlocks(wp.blocks.parse('<!-- wp:paragraph --><p>' + content + '</p><!-- /wp:paragraph -->'));
            }
        };

        /**
         * Effet pour charger les suggestions au montage du composant
         */
        useEffect(function() {
            fetchSuggestions();
        }, []);

        /**
         * Rendu du composant
         */
        return createElement(
            Fragment,
            null,
            createElement(
                PluginSidebarMoreMenuItem,
                {
                    target: "smartseo-ai-suggestions",
                    icon: "superhero"
                },
                smartseoAiSuggestions.i18n.title
            ),
            createElement(
                PluginSidebar,
                {
                    name: "smartseo-ai-suggestions",
                    title: smartseoAiSuggestions.i18n.title,
                    icon: "superhero"
                },
                createElement(
                    PanelBody,
                    null,
                    isLoading ? 
                        createElement(
                            "div",
                            { className: "smartseo-ai-loading" },
                            createElement(Spinner),
                            createElement(
                                "p",
                                null,
                                smartseoAiSuggestions.i18n.loading
                            )
                        ) : 
                        error ? 
                            createElement(
                                Notice,
                                {
                                    status: "error",
                                    isDismissible: false
                                },
                                error
                            ) : 
                            suggestions.length === 0 ? 
                                createElement(
                                    "p",
                                    null,
                                    smartseoAiSuggestions.i18n.no_suggestions
                                ) : 
                                createElement(
                                    "div",
                                    { className: "smartseo-ai-suggestions" },
                                    suggestions.map(function(suggestion, index) {
                                        return createElement(
                                            "div",
                                            {
                                                key: index,
                                                className: "smartseo-ai-suggestion"
                                            },
                                            createElement(
                                                "h3",
                                                null,
                                                suggestion.title
                                            ),
                                            createElement(
                                                "p",
                                                null,
                                                suggestion.content
                                            ),
                                            createElement(
                                                Button,
                                                {
                                                    isPrimary: true,
                                                    onClick: function() {
                                                        applySuggestion(suggestion.content, suggestion.type);
                                                    }
                                                },
                                                smartseoAiSuggestions.i18n.apply
                                            )
                                        );
                                    })
                                ),
                    createElement(
                        Button,
                        {
                            isSecondary: true,
                            onClick: fetchSuggestions,
                            className: "smartseo-ai-refresh-button"
                        },
                        smartseoAiSuggestions.i18n.refresh
                    )
                )
            )
        );
    };

    // Enregistrer le plugin
    registerPlugin('smartseo-ai-suggestions', {
        render: SmartSEOAISuggestions,
        icon: 'superhero'
    });

})(window.wp);
