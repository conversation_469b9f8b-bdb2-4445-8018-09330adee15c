<?php
/**
 * Classe pour les suggestions de liens internes
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour les suggestions de liens internes
 */
class SmartSEO_AI_Internal_Link_Suggestions extends SmartSEO_AI_Suggestion_Base {

    /**
     * Génère des suggestions de liens internes
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @return array Suggestions de liens internes.
     */
    public function generate( $post_id, $content ) {
        // Si le contenu est vide, récupérer le contenu de l'article
        if ( empty( $content ) ) {
            $content = $this->get_post_content( $post_id );
        }
        
        // Récupérer les articles similaires
        $similar_posts = $this->get_similar_posts( $post_id );
        
        // Extraire les liens existants
        $existing_links = $this->extract_links( $content );
        
        // Limiter la taille du contenu pour l'API
        $content = substr( $content, 0, 5000 );
        
        // Préparer les données des articles similaires pour l'IA
        $similar_posts_data = array();
        foreach ( $similar_posts as $post ) {
            $similar_posts_data[] = array(
                'id' => $post['id'],
                'title' => $post['title'],
                'url' => $post['url'],
            );
        }
        
        // Générer le prompt pour l'IA
        $prompt = $this->generate_prompt(
            "Analyse ce contenu et suggère des opportunités d'ajouter des liens internes vers d'autres articles du site. " .
            "Voici une liste d'articles similaires disponibles pour les liens internes : " . json_encode( $similar_posts_data ) . ". " .
            "Identifie les phrases ou mots-clés dans le contenu qui pourraient être liés à ces articles. " .
            "Réponds au format JSON avec un tableau 'link_suggestions' contenant des objets avec 'anchor_text', 'target_post_id', 'target_post_title', 'target_url', 'context' (texte avant et après), 'reason'.",
            array(
                'content' => $content,
            )
        );
        
        // Appeler l'API IA
        $response = $this->call_ai_api( $prompt );
        
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }
        
        // Formater les suggestions
        $suggestions = array(
            'status' => 'success',
            'existing_links' => $existing_links,
            'similar_posts' => $similar_posts,
            'link_suggestions' => isset( $response['link_suggestions'] ) ? $response['link_suggestions'] : array(),
        );
        
        // Ajouter des identifiants uniques pour chaque suggestion
        if ( ! empty( $suggestions['link_suggestions'] ) ) {
            foreach ( $suggestions['link_suggestions'] as $key => $link ) {
                $suggestions['link_suggestions'][$key]['id'] = 'link_' . $key;
            }
        }
        
        // Enregistrer les suggestions en meta
        update_post_meta( $post_id, 'smartseo_ai_internal_link_suggestions', $suggestions );
        
        return $suggestions;
    }

    /**
     * Applique une suggestion de lien interne
     *
     * @param int    $post_id      ID de l'article.
     * @param string $suggestion_id ID de la suggestion.
     * @return mixed Résultat de l'application de la suggestion.
     */
    public function apply( $post_id, $suggestion_id ) {
        // Récupérer les suggestions stockées en meta
        $suggestions = get_post_meta( $post_id, 'smartseo_ai_internal_link_suggestions', true );
        
        if ( empty( $suggestions ) || empty( $suggestions['link_suggestions'] ) ) {
            return new WP_Error( 'no_suggestions', __( 'Aucune suggestion de lien interne disponible.', 'smartseo-ai' ) );
        }
        
        $post = get_post( $post_id );
        if ( ! $post ) {
            return new WP_Error( 'invalid_post', __( 'Article introuvable.', 'smartseo-ai' ) );
        }
        
        // Trouver la suggestion à appliquer
        $link_suggestion = null;
        foreach ( $suggestions['link_suggestions'] as $suggestion ) {
            if ( isset( $suggestion['id'] ) && $suggestion['id'] === $suggestion_id ) {
                $link_suggestion = $suggestion;
                break;
            }
        }
        
        if ( ! $link_suggestion ) {
            return new WP_Error( 'invalid_suggestion', __( 'Suggestion de lien interne invalide.', 'smartseo-ai' ) );
        }
        
        // Appliquer la suggestion
        $content = $post->post_content;
        $anchor_text = $link_suggestion['anchor_text'];
        $target_url = $link_suggestion['target_url'];
        
        // Vérifier si l'ancre existe dans le contenu
        if ( strpos( $content, $anchor_text ) === false ) {
            return new WP_Error(
                'anchor_not_found',
                sprintf(
                    __( 'Le texte d\'ancrage "%s" n\'a pas été trouvé dans le contenu.', 'smartseo-ai' ),
                    $anchor_text
                )
            );
        }
        
        // Remplacer la première occurrence de l'ancre par un lien
        $linked_text = '<a href="' . esc_url( $target_url ) . '">' . $anchor_text . '</a>';
        $content = preg_replace( '/' . preg_quote( $anchor_text, '/' ) . '/', $linked_text, $content, 1 );
        
        // Mettre à jour le contenu de l'article
        wp_update_post( array(
            'ID' => $post_id,
            'post_content' => $content,
        ) );
        
        return array(
            'status' => 'success',
            'message' => sprintf(
                __( 'Lien interne ajouté avec succès vers "%s".', 'smartseo-ai' ),
                $link_suggestion['target_post_title']
            ),
        );
    }

    /**
     * Extrait les liens du contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Liens extraits.
     */
    private function extract_links( $content ) {
        $links = array();
        
        // Extraire les balises a
        preg_match_all( '/<a[^>]+href=([\'"])(?<href>.+?)\1[^>]*>(?<text>.+?)<\/a>/i', $content, $matches );
        
        if ( ! empty( $matches['href'] ) && ! empty( $matches['text'] ) ) {
            foreach ( $matches['href'] as $key => $href ) {
                $links[] = array(
                    'url' => $href,
                    'anchor_text' => strip_tags( $matches['text'][$key] ),
                    'is_internal' => $this->is_internal_link( $href ),
                );
            }
        }
        
        return $links;
    }

    /**
     * Vérifie si un lien est interne
     *
     * @param string $url URL à vérifier.
     * @return bool True si le lien est interne, false sinon.
     */
    private function is_internal_link( $url ) {
        $site_url = site_url();
        $site_host = parse_url( $site_url, PHP_URL_HOST );
        
        // Les liens relatifs sont internes
        if ( strpos( $url, '/' ) === 0 ) {
            return true;
        }
        
        // Vérifier si l'URL commence par le site_url
        if ( strpos( $url, $site_url ) === 0 ) {
            return true;
        }
        
        // Vérifier si l'hôte est le même
        $url_host = parse_url( $url, PHP_URL_HOST );
        if ( $url_host === $site_host ) {
            return true;
        }
        
        return false;
    }
}
