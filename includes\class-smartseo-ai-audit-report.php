<?php
/**
 * Classe pour générer le rapport d'audit SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui génère le rapport d'audit SEO
 */
class SmartSEO_AI_Audit_Report {

    /**
     * Résultats de l'audit
     *
     * @var array
     */
    private $results;

    /**
     * Constructeur
     *
     * @param array $results Résultats de l'audit.
     */
    public function __construct( $results ) {
        $this->results = $results;
    }

    /**
     * Génère le rapport d'audit
     *
     * @return array Rapport d'audit.
     */
    public function generate() {
        $report = array(
            'summary' => $this->generate_summary(),
            'sections' => array(
                'meta' => $this->generate_meta_section(),
                'images' => $this->generate_images_section(),
                'headings' => $this->generate_headings_section(),
                'url' => $this->generate_url_section(),
                'links' => $this->generate_links_section(),
                'keywords' => $this->generate_keywords_section(),
                'robots_sitemap' => $this->generate_robots_sitemap_section(),
                'schema' => $this->generate_schema_section(),
            ),
            'score' => $this->calculate_score(),
        );

        return $report;
    }

    /**
     * Génère le résumé du rapport
     *
     * @return array Résumé du rapport.
     */
    private function generate_summary() {
        $total_issues = 0;
        $critical_issues = 0;
        $important_issues = 0;
        $minor_issues = 0;

        // Compter les problèmes par section
        foreach ( $this->results as $section => $data ) {
            if ( is_array( $data ) && isset( $data['issues'] ) ) {
                $issues_count = count( $data['issues'] );
                $total_issues += $issues_count;

                // Classer les problèmes par gravité
                if ( $section === 'meta' || $section === 'links' || $section === 'robots_sitemap' ) {
                    $critical_issues += $issues_count;
                } elseif ( $section === 'headings' || $section === 'keywords' || $section === 'schema' ) {
                    $important_issues += $issues_count;
                } else {
                    $minor_issues += $issues_count;
                }
            }
        }

        // Déterminer le statut global
        $status = 'good';
        if ( $critical_issues > 0 ) {
            $status = 'critical';
        } elseif ( $important_issues > 0 ) {
            $status = 'warning';
        } elseif ( $minor_issues > 0 ) {
            $status = 'info';
        }

        return array(
            'status' => $status,
            'total_issues' => $total_issues,
            'critical_issues' => $critical_issues,
            'important_issues' => $important_issues,
            'minor_issues' => $minor_issues,
            'score' => $this->calculate_score(),
        );
    }

    /**
     * Génère la section des balises meta
     *
     * @return array Section des balises meta.
     */
    private function generate_meta_section() {
        if ( ! isset( $this->results['meta'] ) || ! is_array( $this->results['meta'] ) ) {
            return array(
                'title' => __( 'Balises Meta', 'smartseo-ai' ),
                'status' => 'error',
                'message' => __( 'Impossible d\'analyser les balises meta.', 'smartseo-ai' ),
            );
        }

        $meta = $this->results['meta'];
        $status = 'good';
        $issues = array();
        $recommendations = array();

        // Récupérer les problèmes et recommandations
        if ( isset( $meta['title']['analysis']['issues'] ) ) {
            $issues = array_merge( $issues, $meta['title']['analysis']['issues'] );
        }
        if ( isset( $meta['meta_description']['analysis']['issues'] ) ) {
            $issues = array_merge( $issues, $meta['meta_description']['analysis']['issues'] );
        }
        if ( isset( $meta['title']['analysis']['recommendations'] ) ) {
            $recommendations = array_merge( $recommendations, $meta['title']['analysis']['recommendations'] );
        }
        if ( isset( $meta['meta_description']['analysis']['recommendations'] ) ) {
            $recommendations = array_merge( $recommendations, $meta['meta_description']['analysis']['recommendations'] );
        }

        // Déterminer le statut
        if ( count( $issues ) > 0 ) {
            $status = 'warning';
        }

        return array(
            'title' => __( 'Balises Meta', 'smartseo-ai' ),
            'status' => $status,
            'score' => isset( $meta['title']['analysis']['score'] ) ? $meta['title']['analysis']['score'] : 0,
            'issues' => $issues,
            'recommendations' => $recommendations,
            'details' => array(
                'title' => isset( $meta['title'] ) ? $meta['title'] : array(),
                'meta_description' => isset( $meta['meta_description'] ) ? $meta['meta_description'] : array(),
            ),
        );
    }

    /**
     * Génère la section des images
     *
     * @return array Section des images.
     */
    private function generate_images_section() {
        if ( ! isset( $this->results['images'] ) || ! is_array( $this->results['images'] ) ) {
            return array(
                'title' => __( 'Images', 'smartseo-ai' ),
                'status' => 'error',
                'message' => __( 'Impossible d\'analyser les images.', 'smartseo-ai' ),
            );
        }

        $images = $this->results['images'];
        $status = 'good';
        $issues = isset( $images['issues'] ) ? $images['issues'] : array();
        $recommendations = isset( $images['recommendations'] ) ? $images['recommendations'] : array();

        // Déterminer le statut
        if ( count( $issues ) > 0 ) {
            $status = 'warning';
        }

        return array(
            'title' => __( 'Images', 'smartseo-ai' ),
            'status' => $status,
            'score' => isset( $images['score'] ) ? $images['score'] : 0,
            'issues' => $issues,
            'recommendations' => $recommendations,
            'details' => array(
                'total_images' => isset( $images['total_images'] ) ? $images['total_images'] : 0,
                'images_with_alt' => isset( $images['images_with_alt'] ) ? $images['images_with_alt'] : 0,
                'images_without_alt' => isset( $images['images_without_alt'] ) ? $images['images_without_alt'] : 0,
                'images_with_empty_alt' => isset( $images['images_with_empty_alt'] ) ? $images['images_with_empty_alt'] : 0,
            ),
        );
    }

    /**
     * Génère la section des balises d'en-tête
     *
     * @return array Section des balises d'en-tête.
     */
    private function generate_headings_section() {
        if ( ! isset( $this->results['headings'] ) || ! is_array( $this->results['headings'] ) ) {
            return array(
                'title' => __( 'Balises d\'en-tête', 'smartseo-ai' ),
                'status' => 'error',
                'message' => __( 'Impossible d\'analyser les balises d\'en-tête.', 'smartseo-ai' ),
            );
        }

        $headings = $this->results['headings'];
        $status = 'good';
        $issues = isset( $headings['issues'] ) ? $headings['issues'] : array();
        $recommendations = isset( $headings['recommendations'] ) ? $headings['recommendations'] : array();

        // Déterminer le statut
        if ( count( $issues ) > 0 ) {
            $status = 'warning';
        }

        return array(
            'title' => __( 'Balises d\'en-tête', 'smartseo-ai' ),
            'status' => $status,
            'score' => isset( $headings['score'] ) ? $headings['score'] : 0,
            'issues' => $issues,
            'recommendations' => $recommendations,
            'details' => array(
                'counts' => isset( $headings['counts'] ) ? $headings['counts'] : array(),
                'headings' => isset( $headings['headings'] ) ? $headings['headings'] : array(),
            ),
        );
    }

    /**
     * Génère la section de l'URL
     *
     * @return array Section de l'URL.
     */
    private function generate_url_section() {
        if ( ! isset( $this->results['url'] ) || ! is_array( $this->results['url'] ) ) {
            return array(
                'title' => __( 'Structure de l\'URL', 'smartseo-ai' ),
                'status' => 'error',
                'message' => __( 'Impossible d\'analyser la structure de l\'URL.', 'smartseo-ai' ),
            );
        }

        $url = $this->results['url'];
        $status = 'good';
        $issues = isset( $url['issues'] ) ? $url['issues'] : array();
        $recommendations = isset( $url['recommendations'] ) ? $url['recommendations'] : array();

        // Déterminer le statut
        if ( count( $issues ) > 0 ) {
            $status = 'warning';
        }

        return array(
            'title' => __( 'Structure de l\'URL', 'smartseo-ai' ),
            'status' => $status,
            'score' => isset( $url['score'] ) ? $url['score'] : 0,
            'issues' => $issues,
            'recommendations' => $recommendations,
            'details' => array(
                'url' => isset( $url['url'] ) ? $url['url'] : '',
                'slug' => isset( $url['slug'] ) ? $url['slug'] : '',
            ),
        );
    }

    /**
     * Génère la section des liens
     *
     * @return array Section des liens.
     */
    private function generate_links_section() {
        if ( ! isset( $this->results['links'] ) || ! is_array( $this->results['links'] ) ) {
            return array(
                'title' => __( 'Liens', 'smartseo-ai' ),
                'status' => 'error',
                'message' => __( 'Impossible d\'analyser les liens.', 'smartseo-ai' ),
            );
        }

        $links = $this->results['links'];
        $status = 'good';
        $issues = isset( $links['issues'] ) ? $links['issues'] : array();
        $recommendations = isset( $links['recommendations'] ) ? $links['recommendations'] : array();

        // Déterminer le statut
        if ( isset( $links['broken_links'] ) && $links['broken_links'] > 0 ) {
            $status = 'critical';
        } elseif ( count( $issues ) > 0 ) {
            $status = 'warning';
        }

        return array(
            'title' => __( 'Liens', 'smartseo-ai' ),
            'status' => $status,
            'score' => isset( $links['score'] ) ? $links['score'] : 0,
            'issues' => $issues,
            'recommendations' => $recommendations,
            'details' => array(
                'total_links' => isset( $links['total_links'] ) ? $links['total_links'] : 0,
                'internal_links' => isset( $links['internal_links'] ) ? $links['internal_links'] : 0,
                'external_links' => isset( $links['external_links'] ) ? $links['external_links'] : 0,
                'broken_links' => isset( $links['broken_links'] ) ? $links['broken_links'] : 0,
                'nofollow_links' => isset( $links['nofollow_links'] ) ? $links['nofollow_links'] : 0,
            ),
        );
    }

    /**
     * Génère la section des mots-clés
     *
     * @return array Section des mots-clés.
     */
    private function generate_keywords_section() {
        if ( ! isset( $this->results['keywords'] ) || ! is_array( $this->results['keywords'] ) ) {
            return array(
                'title' => __( 'Mots-clés', 'smartseo-ai' ),
                'status' => 'error',
                'message' => __( 'Impossible d\'analyser les mots-clés.', 'smartseo-ai' ),
            );
        }

        $keywords = $this->results['keywords'];
        $status = 'good';
        $issues = isset( $keywords['issues'] ) ? $keywords['issues'] : array();
        $recommendations = isset( $keywords['recommendations'] ) ? $keywords['recommendations'] : array();

        // Déterminer le statut
        if ( count( $issues ) > 0 ) {
            $status = 'warning';
        }

        return array(
            'title' => __( 'Mots-clés', 'smartseo-ai' ),
            'status' => $status,
            'score' => isset( $keywords['score'] ) ? $keywords['score'] : 0,
            'issues' => $issues,
            'recommendations' => $recommendations,
            'details' => array(
                'focus_keyword' => isset( $keywords['focus_keyword'] ) ? $keywords['focus_keyword'] : '',
                'keyword_density' => isset( $keywords['keyword_density'] ) ? $keywords['keyword_density'] : 0,
                'keyword_count' => isset( $keywords['keyword_count'] ) ? $keywords['keyword_count'] : 0,
                'total_words' => isset( $keywords['total_words'] ) ? $keywords['total_words'] : 0,
                'top_keywords' => isset( $keywords['top_keywords'] ) ? $keywords['top_keywords'] : array(),
            ),
        );
    }

    /**
     * Génère la section des fichiers robots.txt et sitemap.xml
     *
     * @return array Section des fichiers robots.txt et sitemap.xml.
     */
    private function generate_robots_sitemap_section() {
        if ( ! isset( $this->results['robots_sitemap'] ) || ! is_array( $this->results['robots_sitemap'] ) ) {
            return array(
                'title' => __( 'Robots.txt et Sitemap', 'smartseo-ai' ),
                'status' => 'error',
                'message' => __( 'Impossible d\'analyser les fichiers robots.txt et sitemap.xml.', 'smartseo-ai' ),
            );
        }

        $robots_sitemap = $this->results['robots_sitemap'];
        $status = 'good';
        $issues = array();
        $recommendations = array();

        // Récupérer les problèmes et recommandations
        if ( isset( $robots_sitemap['robots_txt']['issues'] ) ) {
            $issues = array_merge( $issues, $robots_sitemap['robots_txt']['issues'] );
        }
        if ( isset( $robots_sitemap['sitemap']['issues'] ) ) {
            $issues = array_merge( $issues, $robots_sitemap['sitemap']['issues'] );
        }
        if ( isset( $robots_sitemap['robots_txt']['recommendations'] ) ) {
            $recommendations = array_merge( $recommendations, $robots_sitemap['robots_txt']['recommendations'] );
        }
        if ( isset( $robots_sitemap['sitemap']['recommendations'] ) ) {
            $recommendations = array_merge( $recommendations, $robots_sitemap['sitemap']['recommendations'] );
        }

        // Déterminer le statut
        if ( ( isset( $robots_sitemap['robots_txt']['exists'] ) && ! $robots_sitemap['robots_txt']['exists'] ) ||
             ( isset( $robots_sitemap['sitemap']['exists'] ) && ! $robots_sitemap['sitemap']['exists'] ) ) {
            $status = 'critical';
        } elseif ( count( $issues ) > 0 ) {
            $status = 'warning';
        }

        return array(
            'title' => __( 'Robots.txt et Sitemap', 'smartseo-ai' ),
            'status' => $status,
            'score' => isset( $robots_sitemap['score'] ) ? $robots_sitemap['score'] : 0,
            'issues' => $issues,
            'recommendations' => $recommendations,
            'details' => array(
                'robots_txt' => isset( $robots_sitemap['robots_txt'] ) ? $robots_sitemap['robots_txt'] : array(),
                'sitemap' => isset( $robots_sitemap['sitemap'] ) ? $robots_sitemap['sitemap'] : array(),
            ),
        );
    }

    /**
     * Génère la section des données structurées
     *
     * @return array Section des données structurées.
     */
    private function generate_schema_section() {
        if ( ! isset( $this->results['schema'] ) || ! is_array( $this->results['schema'] ) ) {
            return array(
                'title' => __( 'Données structurées', 'smartseo-ai' ),
                'status' => 'error',
                'message' => __( 'Impossible d\'analyser les données structurées.', 'smartseo-ai' ),
            );
        }

        $schema = $this->results['schema'];
        $status = 'good';
        $issues = isset( $schema['issues'] ) ? $schema['issues'] : array();
        $recommendations = isset( $schema['recommendations'] ) ? $schema['recommendations'] : array();

        // Déterminer le statut
        if ( isset( $schema['has_schema'] ) && ! $schema['has_schema'] ) {
            $status = 'warning';
        } elseif ( count( $issues ) > 0 ) {
            $status = 'warning';
        }

        return array(
            'title' => __( 'Données structurées', 'smartseo-ai' ),
            'status' => $status,
            'score' => isset( $schema['score'] ) ? $schema['score'] : 0,
            'issues' => $issues,
            'recommendations' => $recommendations,
            'details' => array(
                'has_schema' => isset( $schema['has_schema'] ) ? $schema['has_schema'] : false,
                'schema_count' => isset( $schema['schema_count'] ) ? $schema['schema_count'] : 0,
                'schema_types' => isset( $schema['schema_types'] ) ? $schema['schema_types'] : array(),
            ),
        );
    }

    /**
     * Calcule le score global de l'audit
     *
     * @return int Score global.
     */
    public function calculate_score() {
        $scores = array();
        $weights = array(
            'meta' => 1.5,
            'images' => 1.0,
            'headings' => 1.0,
            'url' => 1.0,
            'links' => 1.5,
            'keywords' => 1.2,
            'robots_sitemap' => 1.0,
            'schema' => 1.0,
        );

        $total_weight = array_sum( $weights );
        $weighted_score = 0;

        foreach ( $this->results as $section => $data ) {
            if ( isset( $data['score'] ) && isset( $weights[$section] ) ) {
                $weighted_score += $data['score'] * $weights[$section];
            }
        }

        $final_score = $total_weight > 0 ? round( $weighted_score / $total_weight ) : 0;

        return max( 0, min( 100, $final_score ) );
    }
}
