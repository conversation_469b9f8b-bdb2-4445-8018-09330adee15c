<?php
/**
 * Classe pour les suggestions de structure de contenu
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour les suggestions de structure de contenu
 */
class SmartSEO_AI_Content_Structure_Suggestions extends SmartSEO_AI_Suggestion_Base {

    /**
     * Génère des suggestions de structure de contenu
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @return array Suggestions de structure de contenu.
     */
    public function generate( $post_id, $content ) {
        // Si le contenu est vide, récupérer le contenu de l'article
        if ( empty( $content ) ) {
            $content = $this->get_post_content( $post_id );
        }
        
        // Récupérer le mot-clé principal s'il existe
        $focus_keyword = get_post_meta( $post_id, 'smartseo_ai_focus_keyword', true );
        if ( empty( $focus_keyword ) ) {
            $focus_keyword = get_post_meta( $post_id, '_yoast_wpseo_focuskw', true );
        }
        
        // Analyser la structure du contenu
        $paragraphs = $this->extract_paragraphs( $content );
        
        // Limiter la taille du contenu pour l'API
        $content = substr( $content, 0, 5000 );
        
        // Générer le prompt pour l'IA
        $prompt = $this->generate_prompt(
            "Analyse la structure de ce contenu et identifie les problèmes qui pourraient affecter le SEO et l'expérience utilisateur. " .
            "Recherche les paragraphes trop courts (moins de 40 mots) ou trop longs (plus de 300 mots), les sections sans sous-titres, " .
            "et les opportunités d'améliorer la structure avec des listes à puces, des tableaux ou d'autres éléments. " .
            ( ! empty( $focus_keyword ) ? "Le mot-clé principal est : $focus_keyword. " : "" ) .
            "Réponds au format JSON avec : 'structure_issues' (tableau d'objets avec 'type', 'description', 'location', 'suggestion'), " .
            "'improvement_opportunities' (tableau d'objets avec 'type', 'description', 'location', 'suggestion').",
            array(
                'content' => $content,
            )
        );
        
        // Appeler l'API IA
        $response = $this->call_ai_api( $prompt );
        
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }
        
        // Formater les suggestions
        $suggestions = array(
            'status' => 'success',
            'focus_keyword' => $focus_keyword,
            'paragraphs_count' => count( $paragraphs ),
            'structure_issues' => isset( $response['structure_issues'] ) ? $response['structure_issues'] : array(),
            'improvement_opportunities' => isset( $response['improvement_opportunities'] ) ? $response['improvement_opportunities'] : array(),
        );
        
        // Ajouter des identifiants uniques pour chaque suggestion
        if ( ! empty( $suggestions['structure_issues'] ) ) {
            foreach ( $suggestions['structure_issues'] as $key => $issue ) {
                $suggestions['structure_issues'][$key]['id'] = 'issue_' . $key;
            }
        }
        
        if ( ! empty( $suggestions['improvement_opportunities'] ) ) {
            foreach ( $suggestions['improvement_opportunities'] as $key => $opportunity ) {
                $suggestions['improvement_opportunities'][$key]['id'] = 'opportunity_' . $key;
            }
        }
        
        // Enregistrer les suggestions en meta
        update_post_meta( $post_id, 'smartseo_ai_content_structure_suggestions', $suggestions );
        
        return $suggestions;
    }

    /**
     * Applique une suggestion de structure de contenu
     *
     * @param int    $post_id      ID de l'article.
     * @param string $suggestion_id ID de la suggestion.
     * @return mixed Résultat de l'application de la suggestion.
     */
    public function apply( $post_id, $suggestion_id ) {
        // Les suggestions de structure de contenu sont généralement des conseils
        // que l'utilisateur doit appliquer manuellement, car elles nécessitent
        // souvent une réécriture significative du contenu.
        
        return new WP_Error(
            'manual_implementation_required',
            __( 'Les suggestions de structure de contenu nécessitent une implémentation manuelle. Veuillez modifier votre contenu en suivant les recommandations.', 'smartseo-ai' )
        );
    }

    /**
     * Extrait les paragraphes du contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Paragraphes extraits.
     */
    private function extract_paragraphs( $content ) {
        $paragraphs = array();
        
        // Supprimer les balises HTML tout en préservant les paragraphes
        $content_without_html = strip_tags( $content, '<p>' );
        
        // Extraire les paragraphes
        preg_match_all( '/<p>(.*?)<\/p>/is', $content_without_html, $matches );
        
        if ( ! empty( $matches[1] ) ) {
            foreach ( $matches[1] as $key => $paragraph ) {
                // Nettoyer le paragraphe
                $clean_paragraph = trim( strip_tags( $paragraph ) );
                
                if ( ! empty( $clean_paragraph ) ) {
                    // Compter les mots
                    $word_count = str_word_count( $clean_paragraph );
                    
                    $paragraphs[] = array(
                        'text' => $clean_paragraph,
                        'word_count' => $word_count,
                        'index' => $key,
                    );
                }
            }
        }
        
        return $paragraphs;
    }
}
