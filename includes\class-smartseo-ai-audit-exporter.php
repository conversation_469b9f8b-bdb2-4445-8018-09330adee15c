<?php
/**
 * Classe pour l'exportation des résultats d'audit SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'exportation des résultats d'audit SEO
 */
class SmartSEO_AI_Audit_Exporter {

    /**
     * Constructeur
     */
    public function __construct() {
        // Rien à initialiser pour l'instant
    }

    /**
     * Exporte les résultats d'audit au format CSV
     *
     * @param array $results Résultats d'audit.
     * @return string Données CSV.
     */
    public function export_csv( $results ) {
        // Initialiser le flux de sortie
        $output = fopen( 'php://temp', 'r+' );

        // Ajouter l'en-tête UTF-8 BOM pour Excel
        fputs( $output, "\xEF\xBB\xBF" );

        // Ajouter l'en-tête CSV
        fputcsv( $output, array(
            __( 'ID', 'smartseo-ai' ),
            __( 'Titre', 'smartseo-ai' ),
            __( 'URL', 'smartseo-ai' ),
            __( 'Type', 'smartseo-ai' ),
            __( 'Score SEO', 'smartseo-ai' ),
            __( 'Problèmes', 'smartseo-ai' ),
            __( 'Date d\'audit', 'smartseo-ai' ),
        ) );

        // Ajouter les données
        foreach ( $results as $result ) {
            fputcsv( $output, array(
                $result['post_id'],
                $result['title'],
                $result['url'],
                $result['post_type'],
                $result['score'],
                $result['issues'],
                $result['timestamp'],
            ) );
        }

        // Récupérer le contenu du flux
        rewind( $output );
        $csv = stream_get_contents( $output );
        fclose( $output );

        return $csv;
    }

    /**
     * Exporte les résultats d'audit au format PDF
     *
     * @param array $results Résultats d'audit.
     * @return string Données PDF encodées en base64.
     */
    public function export_pdf( $results ) {
        // Vérifier si la bibliothèque TCPDF est disponible
        if ( ! class_exists( 'TCPDF' ) ) {
            // Inclure la bibliothèque TCPDF (à adapter selon votre installation)
            require_once SMARTSEO_AI_PLUGIN_DIR . 'vendor/tcpdf/tcpdf.php';
        }

        // Créer un nouveau document PDF
        $pdf = new TCPDF( 'L', 'mm', 'A4', true, 'UTF-8', false );

        // Définir les informations du document
        $pdf->SetCreator( 'SmartSEO AI' );
        $pdf->SetAuthor( 'SmartSEO AI' );
        $pdf->SetTitle( __( 'Rapport d\'audit SEO', 'smartseo-ai' ) );
        $pdf->SetSubject( __( 'Rapport d\'audit SEO', 'smartseo-ai' ) );
        $pdf->SetKeywords( 'SEO, Audit, SmartSEO AI' );

        // Supprimer les en-têtes et pieds de page par défaut
        $pdf->setPrintHeader( false );
        $pdf->setPrintFooter( false );

        // Définir les marges
        $pdf->SetMargins( 10, 10, 10 );
        $pdf->SetAutoPageBreak( true, 10 );

        // Ajouter une page
        $pdf->AddPage();

        // Définir la police
        $pdf->SetFont( 'helvetica', 'B', 16 );

        // Ajouter le titre
        $pdf->Cell( 0, 10, __( 'Rapport d\'audit SEO', 'smartseo-ai' ), 0, 1, 'C' );
        $pdf->Ln( 5 );

        // Ajouter la date
        $pdf->SetFont( 'helvetica', '', 10 );
        $pdf->Cell( 0, 5, __( 'Date du rapport', 'smartseo-ai' ) . ': ' . date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ) ), 0, 1, 'R' );
        $pdf->Ln( 5 );

        // Ajouter le tableau des résultats
        $pdf->SetFont( 'helvetica', 'B', 10 );
        $pdf->SetFillColor( 240, 240, 240 );

        // En-têtes du tableau
        $pdf->Cell( 15, 7, __( 'ID', 'smartseo-ai' ), 1, 0, 'C', true );
        $pdf->Cell( 80, 7, __( 'Titre', 'smartseo-ai' ), 1, 0, 'C', true );
        $pdf->Cell( 80, 7, __( 'URL', 'smartseo-ai' ), 1, 0, 'C', true );
        $pdf->Cell( 25, 7, __( 'Type', 'smartseo-ai' ), 1, 0, 'C', true );
        $pdf->Cell( 20, 7, __( 'Score', 'smartseo-ai' ), 1, 0, 'C', true );
        $pdf->Cell( 20, 7, __( 'Problèmes', 'smartseo-ai' ), 1, 0, 'C', true );
        $pdf->Cell( 35, 7, __( 'Date d\'audit', 'smartseo-ai' ), 1, 1, 'C', true );

        // Données du tableau
        $pdf->SetFont( 'helvetica', '', 9 );
        $fill = false;

        foreach ( $results as $result ) {
            // Limiter la longueur du titre et de l'URL
            $title = mb_strlen( $result['title'] ) > 40 ? mb_substr( $result['title'], 0, 37 ) . '...' : $result['title'];
            $url = mb_strlen( $result['url'] ) > 40 ? mb_substr( $result['url'], 0, 37 ) . '...' : $result['url'];

            // Définir la couleur de fond en fonction du score
            if ( $result['score'] >= 80 ) {
                $pdf->SetFillColor( 223, 240, 216 ); // Vert clair
            } elseif ( $result['score'] >= 50 ) {
                $pdf->SetFillColor( 252, 248, 227 ); // Jaune clair
            } else {
                $pdf->SetFillColor( 242, 222, 222 ); // Rouge clair
            }

            $pdf->Cell( 15, 6, $result['post_id'], 1, 0, 'C', true );
            $pdf->Cell( 80, 6, $title, 1, 0, 'L', true );
            $pdf->Cell( 80, 6, $url, 1, 0, 'L', true );
            $pdf->Cell( 25, 6, $result['post_type'], 1, 0, 'C', true );
            $pdf->Cell( 20, 6, $result['score'], 1, 0, 'C', true );
            $pdf->Cell( 20, 6, $result['issues'], 1, 0, 'C', true );
            $pdf->Cell( 35, 6, $result['timestamp'], 1, 1, 'C', true );

            // Réinitialiser la couleur de fond
            $pdf->SetFillColor( 240, 240, 240 );
        }

        // Ajouter un résumé
        $pdf->Ln( 10 );
        $pdf->SetFont( 'helvetica', 'B', 12 );
        $pdf->Cell( 0, 10, __( 'Résumé', 'smartseo-ai' ), 0, 1 );
        $pdf->SetFont( 'helvetica', '', 10 );

        // Calculer les statistiques
        $total_pages = count( $results );
        $total_issues = array_sum( array_column( $results, 'issues' ) );
        $avg_score = $total_pages > 0 ? round( array_sum( array_column( $results, 'score' ) ) / $total_pages ) : 0;

        $pdf->Cell( 0, 6, __( 'Nombre de pages analysées', 'smartseo-ai' ) . ': ' . $total_pages, 0, 1 );
        $pdf->Cell( 0, 6, __( 'Nombre total de problèmes', 'smartseo-ai' ) . ': ' . $total_issues, 0, 1 );
        $pdf->Cell( 0, 6, __( 'Score SEO moyen', 'smartseo-ai' ) . ': ' . $avg_score, 0, 1 );

        // Générer le PDF
        $pdf_data = $pdf->Output( '', 'S' );

        // Encoder en base64
        return base64_encode( $pdf_data );
    }
}
