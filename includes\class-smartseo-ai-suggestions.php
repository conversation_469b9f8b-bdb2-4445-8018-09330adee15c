<?php
/**
 * Classe principale pour les suggestions SEO basées sur l'IA
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les suggestions SEO basées sur l'IA
 */
class SmartSEO_AI_Suggestions {

    /**
     * Instance de la classe
     *
     * @var SmartSEO_AI_Suggestions
     */
    private static $instance = null;

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    private $ai_api;

    /**
     * Résultats des suggestions
     *
     * @var array
     */
    private $suggestions = array();

    /**
     * Constructeur
     */
    public function __construct() {
        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $this->ai_api = new SmartSEO_AI_API();
        } else {
            $this->ai_api = new SmartSEO_AI_Gemini();
        }

        // Ajouter les hooks
        add_action( 'admin_menu', array( $this, 'add_suggestions_menu' ) );
        add_action( 'add_meta_boxes', array( $this, 'add_suggestions_meta_box' ) );
        add_action( 'wp_ajax_smartseo_ai_get_suggestions', array( $this, 'ajax_get_suggestions' ) );
        add_action( 'wp_ajax_smartseo_ai_apply_suggestion', array( $this, 'ajax_apply_suggestion' ) );

        // Ajouter le bouton dans l'éditeur Gutenberg
        add_action( 'enqueue_block_editor_assets', array( $this, 'enqueue_gutenberg_assets' ) );
    }

    /**
     * Ajoute le sous-menu pour les suggestions SEO
     */
    public function add_suggestions_menu() {
        add_submenu_page(
            'smartseo-ai',
            __( 'Suggestions IA', 'smartseo-ai' ),
            __( 'Suggestions IA', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai-suggestions',
            array( $this, 'render_suggestions_page' )
        );
    }

    /**
     * Affiche la page des suggestions SEO
     */
    public function render_suggestions_page() {
        // Inclure le template
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/seo-suggestions.php';
    }

    /**
     * Ajoute une meta box pour les suggestions SEO dans l'éditeur classique
     */
    public function add_suggestions_meta_box() {
        add_meta_box(
            'smartseo_ai_suggestions',
            __( 'Suggestions SEO (IA)', 'smartseo-ai' ),
            array( $this, 'render_suggestions_meta_box' ),
            array( 'post', 'page' ),
            'normal',
            'high'
        );
    }

    /**
     * Affiche la meta box des suggestions SEO
     *
     * @param WP_Post $post Objet post.
     */
    public function render_suggestions_meta_box( $post ) {
        // Inclure le template
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/partials/suggestions-meta-box.php';
    }

    /**
     * Enqueue les assets pour l'éditeur Gutenberg
     */
    public function enqueue_gutenberg_assets() {
        wp_enqueue_script(
            'smartseo-ai-gutenberg-suggestions',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-gutenberg-suggestions.js',
            array( 'wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n', 'wp-data' ),
            SMARTSEO_AI_VERSION,
            true
        );

        wp_localize_script(
            'smartseo-ai-gutenberg-suggestions',
            'smartseoAiSuggestions',
            array(
                'ajaxUrl' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'smartseo_ai_nonce' ),
                'i18n' => array(
                    'title' => __( 'Suggestions SEO (IA)', 'smartseo-ai' ),
                    'analyze' => __( 'Analyser le contenu', 'smartseo-ai' ),
                    'analyzing' => __( 'Analyse en cours...', 'smartseo-ai' ),
                    'apply' => __( 'Appliquer', 'smartseo-ai' ),
                    'applied' => __( 'Appliqué', 'smartseo-ai' ),
                    'error' => __( 'Erreur lors de l\'analyse.', 'smartseo-ai' ),
                ),
            )
        );
    }

    /**
     * Récupère les suggestions SEO via AJAX
     */
    public function ajax_get_suggestions() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $content = isset( $_POST['content'] ) ? sanitize_textarea_field( wp_unslash( $_POST['content'] ) ) : '';

        if ( empty( $post_id ) && empty( $content ) ) {
            wp_send_json_error( array( 'message' => __( 'Veuillez fournir un ID d\'article ou du contenu.', 'smartseo-ai' ) ) );
        }

        // Récupérer le contenu si on a un ID d'article
        if ( $post_id > 0 && empty( $content ) ) {
            $post = get_post( $post_id );
            if ( $post ) {
                $content = $post->post_content;
            }
        }

        // Vérifier si l'API est configurée
        $provider = get_option( 'smartseo_ai_provider', 'openai' );
        $api_key = '';

        if ( $provider === 'openai' ) {
            $api_key = get_option( 'smartseo_ai_openai_api_key', '' );
            if ( empty( $api_key ) ) {
                wp_send_json_error( array( 'message' => __( 'Clé API OpenAI non configurée. Veuillez la configurer dans les paramètres du plugin.', 'smartseo-ai' ) ) );
                return;
            }
        } else {
            $api_key = get_option( 'smartseo_ai_gemini_api_key', '' );
            if ( empty( $api_key ) ) {
                wp_send_json_error( array( 'message' => __( 'Clé API Gemini non configurée. Veuillez la configurer dans les paramètres du plugin.', 'smartseo-ai' ) ) );
                return;
            }
        }

        try {
            // Générer les suggestions
            $suggestions = $this->generate_suggestions( $post_id, $content );

            // Vérifier si une erreur est retournée
            if ( isset( $suggestions['status'] ) && $suggestions['status'] === 'error' ) {
                wp_send_json_error( array( 'message' => $suggestions['message'] ) );
                return;
            }

            // Envoyer les résultats
            wp_send_json_success( array( 'suggestions' => $suggestions ) );
        } catch ( Exception $e ) {
            // Journaliser l'erreur
            error_log( 'SmartSEO AI - Erreur lors de la génération des suggestions : ' . $e->getMessage() );

            // Envoyer l'erreur au client
            wp_send_json_error( array( 'message' => __( 'Une erreur s\'est produite lors de la génération des suggestions : ', 'smartseo-ai' ) . $e->getMessage() ) );
        }
    }

    /**
     * Applique une suggestion via AJAX
     */
    public function ajax_apply_suggestion() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $suggestion_type = isset( $_POST['suggestion_type'] ) ? sanitize_text_field( wp_unslash( $_POST['suggestion_type'] ) ) : '';
        $suggestion_id = isset( $_POST['suggestion_id'] ) ? sanitize_text_field( wp_unslash( $_POST['suggestion_id'] ) ) : '';

        if ( empty( $post_id ) || empty( $suggestion_type ) || empty( $suggestion_id ) ) {
            wp_send_json_error( array( 'message' => __( 'Paramètres manquants.', 'smartseo-ai' ) ) );
        }

        // Appliquer la suggestion
        $result = $this->apply_suggestion( $post_id, $suggestion_type, $suggestion_id );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        // Envoyer les résultats
        wp_send_json_success( array( 'result' => $result ) );
    }

    /**
     * Génère les suggestions SEO
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @return array Suggestions SEO.
     */
    public function generate_suggestions( $post_id, $content ) {
        $this->suggestions = array();

        // Initialiser les classes de suggestions
        $keyword_suggestions = new SmartSEO_AI_Keyword_Suggestions( $this->ai_api );
        $meta_suggestions = new SmartSEO_AI_Meta_Suggestions( $this->ai_api );
        $heading_suggestions = new SmartSEO_AI_Heading_Suggestions( $this->ai_api );
        $content_structure_suggestions = new SmartSEO_AI_Content_Structure_Suggestions( $this->ai_api );
        $internal_link_suggestions = new SmartSEO_AI_Internal_Link_Suggestions( $this->ai_api );
        $image_alt_suggestions = new SmartSEO_AI_Image_Alt_Suggestions( $this->ai_api );
        $readability_suggestions = new SmartSEO_AI_Readability_Suggestions( $this->ai_api );
        $rich_snippet_suggestions = new SmartSEO_AI_Rich_Snippet_Suggestions( $this->ai_api );

        // Générer les suggestions
        $this->suggestions['keywords'] = $keyword_suggestions->generate( $post_id, $content );
        $this->suggestions['meta'] = $meta_suggestions->generate( $post_id, $content );
        $this->suggestions['headings'] = $heading_suggestions->generate( $post_id, $content );
        $this->suggestions['content_structure'] = $content_structure_suggestions->generate( $post_id, $content );
        $this->suggestions['internal_links'] = $internal_link_suggestions->generate( $post_id, $content );
        $this->suggestions['image_alt'] = $image_alt_suggestions->generate( $post_id, $content );
        $this->suggestions['readability'] = $readability_suggestions->generate( $post_id, $content );
        $this->suggestions['rich_snippets'] = $rich_snippet_suggestions->generate( $post_id, $content );

        return $this->suggestions;
    }

    /**
     * Applique une suggestion
     *
     * @param int    $post_id        ID de l'article.
     * @param string $suggestion_type Type de suggestion.
     * @param string $suggestion_id   ID de la suggestion.
     * @return mixed Résultat de l'application de la suggestion.
     */
    public function apply_suggestion( $post_id, $suggestion_type, $suggestion_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return new WP_Error( 'invalid_post', __( 'Article introuvable.', 'smartseo-ai' ) );
        }

        switch ( $suggestion_type ) {
            case 'keywords':
                $keyword_suggestions = new SmartSEO_AI_Keyword_Suggestions( $this->ai_api );
                return $keyword_suggestions->apply( $post_id, $suggestion_id );

            case 'meta':
                $meta_suggestions = new SmartSEO_AI_Meta_Suggestions( $this->ai_api );
                return $meta_suggestions->apply( $post_id, $suggestion_id );

            case 'headings':
                $heading_suggestions = new SmartSEO_AI_Heading_Suggestions( $this->ai_api );
                return $heading_suggestions->apply( $post_id, $suggestion_id );

            case 'content_structure':
                $content_structure_suggestions = new SmartSEO_AI_Content_Structure_Suggestions( $this->ai_api );
                return $content_structure_suggestions->apply( $post_id, $suggestion_id );

            case 'internal_links':
                $internal_link_suggestions = new SmartSEO_AI_Internal_Link_Suggestions( $this->ai_api );
                return $internal_link_suggestions->apply( $post_id, $suggestion_id );

            case 'image_alt':
                $image_alt_suggestions = new SmartSEO_AI_Image_Alt_Suggestions( $this->ai_api );
                return $image_alt_suggestions->apply( $post_id, $suggestion_id );

            case 'readability':
                $readability_suggestions = new SmartSEO_AI_Readability_Suggestions( $this->ai_api );
                return $readability_suggestions->apply( $post_id, $suggestion_id );

            case 'rich_snippets':
                $rich_snippet_suggestions = new SmartSEO_AI_Rich_Snippet_Suggestions( $this->ai_api );
                return $rich_snippet_suggestions->apply( $post_id, $suggestion_id );

            default:
                return new WP_Error( 'invalid_type', __( 'Type de suggestion invalide.', 'smartseo-ai' ) );
        }
    }

    /**
     * Retourne l'instance unique de la classe
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}
