<?php
/**
 * Classe pour les suggestions de mots-clés
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour les suggestions de mots-clés
 */
class SmartSEO_AI_Keyword_Suggestions extends SmartSEO_AI_Suggestion_Base {

    /**
     * Génère des suggestions de mots-clés
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @return array Suggestions de mots-clés.
     */
    public function generate( $post_id, $content ) {
        $title = $this->get_post_title( $post_id );
        $excerpt = $this->get_post_excerpt( $post_id );
        
        // Si le contenu est vide, récupérer le contenu de l'article
        if ( empty( $content ) ) {
            $content = $this->get_post_content( $post_id );
        }
        
        // Limiter la taille du contenu pour l'API
        $content = substr( $content, 0, 5000 );
        
        // Générer le prompt pour l'IA
        $prompt = $this->generate_prompt(
            "Analyse ce contenu et suggère des mots-clés stratégiques pour le SEO. Identifie le mot-clé principal et des mots-clés secondaires pertinents. Pour chaque mot-clé, explique pourquoi il est pertinent et où l'insérer dans le contenu. Réponds au format JSON avec les champs suivants : 'primary_keyword' (objet avec 'keyword', 'relevance', 'placement'), 'secondary_keywords' (tableau d'objets avec 'keyword', 'relevance', 'placement').",
            array(
                'title' => $title,
                'content' => $content,
                'excerpt' => $excerpt,
            )
        );
        
        // Appeler l'API IA
        $response = $this->call_ai_api( $prompt );
        
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }
        
        // Formater les suggestions
        $suggestions = array(
            'status' => 'success',
            'primary_keyword' => isset( $response['primary_keyword'] ) ? $response['primary_keyword'] : array(),
            'secondary_keywords' => isset( $response['secondary_keywords'] ) ? $response['secondary_keywords'] : array(),
        );
        
        // Ajouter des identifiants uniques pour chaque suggestion
        if ( ! empty( $suggestions['primary_keyword'] ) ) {
            $suggestions['primary_keyword']['id'] = 'primary';
        }
        
        if ( ! empty( $suggestions['secondary_keywords'] ) ) {
            foreach ( $suggestions['secondary_keywords'] as $key => $keyword ) {
                $suggestions['secondary_keywords'][$key]['id'] = 'secondary_' . $key;
            }
        }
        
        return $suggestions;
    }

    /**
     * Applique une suggestion de mot-clé
     *
     * @param int    $post_id      ID de l'article.
     * @param string $suggestion_id ID de la suggestion.
     * @return mixed Résultat de l'application de la suggestion.
     */
    public function apply( $post_id, $suggestion_id ) {
        // Récupérer les suggestions stockées en meta
        $suggestions = get_post_meta( $post_id, 'smartseo_ai_keyword_suggestions', true );
        
        if ( empty( $suggestions ) ) {
            return new WP_Error( 'no_suggestions', __( 'Aucune suggestion disponible.', 'smartseo-ai' ) );
        }
        
        // Trouver la suggestion à appliquer
        $keyword = '';
        
        if ( $suggestion_id === 'primary' && ! empty( $suggestions['primary_keyword'] ) ) {
            $keyword = $suggestions['primary_keyword']['keyword'];
        } elseif ( strpos( $suggestion_id, 'secondary_' ) === 0 && ! empty( $suggestions['secondary_keywords'] ) ) {
            $index = substr( $suggestion_id, 10 );
            if ( isset( $suggestions['secondary_keywords'][$index] ) ) {
                $keyword = $suggestions['secondary_keywords'][$index]['keyword'];
            }
        }
        
        if ( empty( $keyword ) ) {
            return new WP_Error( 'invalid_suggestion', __( 'Suggestion invalide.', 'smartseo-ai' ) );
        }
        
        // Enregistrer le mot-clé comme mot-clé principal
        update_post_meta( $post_id, 'smartseo_ai_focus_keyword', $keyword );
        
        // Si Yoast SEO est actif, enregistrer également le mot-clé dans Yoast
        if ( defined( 'WPSEO_VERSION' ) ) {
            update_post_meta( $post_id, '_yoast_wpseo_focuskw', $keyword );
        }
        
        return array(
            'status' => 'success',
            'message' => sprintf( __( 'Mot-clé "%s" défini comme mot-clé principal.', 'smartseo-ai' ), $keyword ),
        );
    }
}
