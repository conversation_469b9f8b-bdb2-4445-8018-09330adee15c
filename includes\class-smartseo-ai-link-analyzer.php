<?php
/**
 * Classe pour analyser les liens
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui analyse les liens et détecte les liens cassés
 */
class SmartSEO_AI_Link_Analyzer {

    /**
     * Constructeur
     */
    public function __construct() {
        // Rien à initialiser pour l'instant
    }

    /**
     * Analyse les liens d'un article
     *
     * @param int $post_id ID de l'article à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_post( $post_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return array(
                'status' => 'error',
                'message' => __( 'Article introuvable.', 'smartseo-ai' ),
            );
        }

        // Récupérer le contenu de l'article
        $content = $post->post_content;

        // Analyser les liens dans le contenu
        return $this->analyze_content_links( $content );
    }

    /**
     * Analyse les liens d'une URL
     *
     * @param string $url URL à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_url( $url ) {
        // Récupérer le contenu de la page
        $response = wp_remote_get( $url );
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }

        $html = wp_remote_retrieve_body( $response );
        if ( empty( $html ) ) {
            return array(
                'status' => 'error',
                'message' => __( 'Impossible de récupérer le contenu de la page.', 'smartseo-ai' ),
            );
        }

        // Créer un objet DOMDocument
        $doc = new DOMDocument();
        @$doc->loadHTML( mb_convert_encoding( $html, 'HTML-ENTITIES', 'UTF-8' ) );
        $xpath = new DOMXPath( $doc );

        // Récupérer tous les liens
        $links = $xpath->query( '//a[@href]' );
        $link_data = array();
        $internal_links = 0;
        $external_links = 0;
        $broken_links = 0;
        $nofollow_links = 0;
        $empty_links = 0;
        $total_links = $links->length;

        $site_url = site_url();
        $site_host = parse_url( $site_url, PHP_URL_HOST );

        foreach ( $links as $link ) {
            $href = $link->getAttribute( 'href' );
            $text = $link->nodeValue;
            $rel = $link->getAttribute( 'rel' );
            $is_nofollow = strpos( $rel, 'nofollow' ) !== false;

            // Ignorer les liens vides ou les ancres
            if ( empty( $href ) || $href === '#' ) {
                $empty_links++;
                continue;
            }

            // Déterminer si le lien est interne ou externe
            $is_internal = false;
            if ( strpos( $href, $site_url ) === 0 || strpos( $href, '/' ) === 0 ) {
                $is_internal = true;
                $internal_links++;
            } else {
                $link_host = parse_url( $href, PHP_URL_HOST );
                if ( $link_host === $site_host ) {
                    $is_internal = true;
                    $internal_links++;
                } else {
                    $external_links++;
                }
            }

            if ( $is_nofollow ) {
                $nofollow_links++;
            }

            // Vérifier si le lien est cassé
            $is_broken = false;
            if ( $is_internal ) {
                // Pour les liens internes, on vérifie s'ils existent
                $full_url = strpos( $href, '/' ) === 0 ? $site_url . $href : $href;
                $response = wp_remote_head( $full_url, array( 'timeout' => 5 ) );
                $response_code = wp_remote_retrieve_response_code( $response );
                $is_broken = $response_code >= 400;
            }

            if ( $is_broken ) {
                $broken_links++;
            }

            $link_data[] = array(
                'url' => $href,
                'text' => $text,
                'is_internal' => $is_internal,
                'is_nofollow' => $is_nofollow,
                'is_broken' => $is_broken,
            );
        }

        // Calculer le score
        $score = 100;
        if ( $total_links > 0 ) {
            // Pénalité pour les liens cassés
            $score -= ( $broken_links / $total_links ) * 100;
            
            // Pénalité pour les liens vides
            $score -= ( $empty_links / $total_links ) * 50;
        }

        // Préparer les résultats
        $results = array(
            'status' => 'success',
            'total_links' => $total_links,
            'internal_links' => $internal_links,
            'external_links' => $external_links,
            'broken_links' => $broken_links,
            'nofollow_links' => $nofollow_links,
            'empty_links' => $empty_links,
            'links' => $link_data,
            'score' => round( max( 0, $score ) ),
        );

        // Ajouter des recommandations
        $results['issues'] = array();
        $results['recommendations'] = array();

        if ( $broken_links > 0 ) {
            $results['issues'][] = sprintf(
                _n(
                    '%d lien cassé a été détecté.',
                    '%d liens cassés ont été détectés.',
                    $broken_links,
                    'smartseo-ai'
                ),
                $broken_links
            );
            $results['recommendations'][] = __( 'Corrigez ou supprimez les liens cassés.', 'smartseo-ai' );
        }

        if ( $empty_links > 0 ) {
            $results['issues'][] = sprintf(
                _n(
                    '%d lien vide a été détecté.',
                    '%d liens vides ont été détectés.',
                    $empty_links,
                    'smartseo-ai'
                ),
                $empty_links
            );
            $results['recommendations'][] = __( 'Supprimez les liens vides ou ajoutez-leur une URL valide.', 'smartseo-ai' );
        }

        if ( $internal_links === 0 ) {
            $results['issues'][] = __( 'Aucun lien interne n\'a été détecté.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Ajoutez des liens internes pour améliorer la navigation et le référencement.', 'smartseo-ai' );
        }

        return $results;
    }

    /**
     * Analyse les liens dans le contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_content_links( $content ) {
        // Créer un objet DOMDocument
        $doc = new DOMDocument();
        @$doc->loadHTML( mb_convert_encoding( $content, 'HTML-ENTITIES', 'UTF-8' ) );
        $xpath = new DOMXPath( $doc );

        // Récupérer tous les liens
        $links = $xpath->query( '//a[@href]' );
        $link_data = array();
        $internal_links = 0;
        $external_links = 0;
        $broken_links = 0;
        $nofollow_links = 0;
        $empty_links = 0;
        $total_links = $links->length;

        $site_url = site_url();
        $site_host = parse_url( $site_url, PHP_URL_HOST );

        foreach ( $links as $link ) {
            $href = $link->getAttribute( 'href' );
            $text = $link->nodeValue;
            $rel = $link->getAttribute( 'rel' );
            $is_nofollow = strpos( $rel, 'nofollow' ) !== false;

            // Ignorer les liens vides ou les ancres
            if ( empty( $href ) || $href === '#' ) {
                $empty_links++;
                continue;
            }

            // Déterminer si le lien est interne ou externe
            $is_internal = false;
            if ( strpos( $href, $site_url ) === 0 || strpos( $href, '/' ) === 0 ) {
                $is_internal = true;
                $internal_links++;
            } else {
                $link_host = parse_url( $href, PHP_URL_HOST );
                if ( $link_host === $site_host ) {
                    $is_internal = true;
                    $internal_links++;
                } else {
                    $external_links++;
                }
            }

            if ( $is_nofollow ) {
                $nofollow_links++;
            }

            // Vérifier si le lien est cassé
            $is_broken = false;
            if ( $is_internal ) {
                // Pour les liens internes, on vérifie s'ils existent
                $full_url = strpos( $href, '/' ) === 0 ? $site_url . $href : $href;
                $response = wp_remote_head( $full_url, array( 'timeout' => 5 ) );
                $response_code = wp_remote_retrieve_response_code( $response );
                $is_broken = $response_code >= 400;
            }

            if ( $is_broken ) {
                $broken_links++;
            }

            $link_data[] = array(
                'url' => $href,
                'text' => $text,
                'is_internal' => $is_internal,
                'is_nofollow' => $is_nofollow,
                'is_broken' => $is_broken,
            );
        }

        // Calculer le score
        $score = 100;
        if ( $total_links > 0 ) {
            // Pénalité pour les liens cassés
            $score -= ( $broken_links / $total_links ) * 100;
            
            // Pénalité pour les liens vides
            $score -= ( $empty_links / $total_links ) * 50;
        }

        // Préparer les résultats
        $results = array(
            'status' => 'success',
            'total_links' => $total_links,
            'internal_links' => $internal_links,
            'external_links' => $external_links,
            'broken_links' => $broken_links,
            'nofollow_links' => $nofollow_links,
            'empty_links' => $empty_links,
            'links' => $link_data,
            'score' => round( max( 0, $score ) ),
        );

        // Ajouter des recommandations
        $results['issues'] = array();
        $results['recommendations'] = array();

        if ( $broken_links > 0 ) {
            $results['issues'][] = sprintf(
                _n(
                    '%d lien cassé a été détecté.',
                    '%d liens cassés ont été détectés.',
                    $broken_links,
                    'smartseo-ai'
                ),
                $broken_links
            );
            $results['recommendations'][] = __( 'Corrigez ou supprimez les liens cassés.', 'smartseo-ai' );
        }

        if ( $empty_links > 0 ) {
            $results['issues'][] = sprintf(
                _n(
                    '%d lien vide a été détecté.',
                    '%d liens vides ont été détectés.',
                    $empty_links,
                    'smartseo-ai'
                ),
                $empty_links
            );
            $results['recommendations'][] = __( 'Supprimez les liens vides ou ajoutez-leur une URL valide.', 'smartseo-ai' );
        }

        if ( $internal_links === 0 ) {
            $results['issues'][] = __( 'Aucun lien interne n\'a été détecté.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Ajoutez des liens internes pour améliorer la navigation et le référencement.', 'smartseo-ai' );
        }

        return $results;
    }
}
