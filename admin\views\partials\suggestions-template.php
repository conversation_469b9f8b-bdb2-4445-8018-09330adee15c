<?php
/**
 * Template pour les suggestions SEO basées sur l'IA
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<script id="suggestions-template" type="text/x-handlebars-template">
    <div class="smartseo-ai-suggestions-header">
        <h2><?php esc_html_e( 'Suggestions d\'optimisation SEO', 'smartseo-ai' ); ?></h2>
        <p><?php esc_html_e( 'Voici les suggestions d\'optimisation SEO générées par l\'intelligence artificielle pour votre contenu.', 'smartseo-ai' ); ?></p>
        <div class="smartseo-ai-suggestions-actions">
            <a href="{{#if post_id}}<?php echo esc_url( admin_url( 'post.php?action=edit&post=' ) ); ?>{{post_id}}{{/if}}" class="button button-secondary" target="_blank">
                <span class="dashicons dashicons-edit"></span>
                <?php esc_html_e( 'Modifier l\'article', 'smartseo-ai' ); ?>
            </a>
            <a href="{{#if post_id}}<?php echo esc_url( admin_url( 'admin.php?page=smartseo-ai-audit&post_id=' ) ); ?>{{post_id}}{{/if}}" class="button button-secondary">
                <span class="dashicons dashicons-chart-bar"></span>
                <?php esc_html_e( 'Voir l\'audit SEO complet', 'smartseo-ai' ); ?>
            </a>
        </div>
    </div>
    
    <div class="smartseo-ai-suggestions-sections">
        <!-- Suggestions de mots-clés -->
        {{#if suggestions.keywords}}
            <div class="smartseo-ai-accordion">
                <div class="smartseo-ai-accordion-header">
                    <span class="dashicons dashicons-tag"></span>
                    <h3><?php esc_html_e( 'Mots-clés stratégiques', 'smartseo-ai' ); ?></h3>
                    <span class="smartseo-ai-accordion-toggle dashicons dashicons-arrow-down-alt2"></span>
                </div>
                
                <div class="smartseo-ai-accordion-content">
                    {{#if suggestions.keywords.primary_keyword}}
                        <div class="smartseo-ai-suggestion-item">
                            <div class="smartseo-ai-suggestion-header">
                                <h4><?php esc_html_e( 'Mot-clé principal recommandé', 'smartseo-ai' ); ?></h4>
                                <button class="button smartseo-ai-apply-suggestion" data-type="keywords" data-id="{{suggestions.keywords.primary_keyword.id}}">
                                    <span class="dashicons dashicons-yes"></span>
                                    <?php esc_html_e( 'Appliquer', 'smartseo-ai' ); ?>
                                </button>
                            </div>
                            <div class="smartseo-ai-suggestion-content">
                                <div class="smartseo-ai-keyword">{{suggestions.keywords.primary_keyword.keyword}}</div>
                                <div class="smartseo-ai-suggestion-details">
                                    <p><strong><?php esc_html_e( 'Pertinence', 'smartseo-ai' ); ?>:</strong> {{suggestions.keywords.primary_keyword.relevance}}</p>
                                    <p><strong><?php esc_html_e( 'Placement recommandé', 'smartseo-ai' ); ?>:</strong> {{suggestions.keywords.primary_keyword.placement}}</p>
                                </div>
                            </div>
                        </div>
                    {{/if}}
                    
                    {{#if suggestions.keywords.secondary_keywords.length}}
                        <h4><?php esc_html_e( 'Mots-clés secondaires recommandés', 'smartseo-ai' ); ?></h4>
                        {{#each suggestions.keywords.secondary_keywords}}
                            <div class="smartseo-ai-suggestion-item">
                                <div class="smartseo-ai-suggestion-header">
                                    <div class="smartseo-ai-keyword">{{this.keyword}}</div>
                                    <button class="button smartseo-ai-apply-suggestion" data-type="keywords" data-id="{{this.id}}">
                                        <span class="dashicons dashicons-yes"></span>
                                        <?php esc_html_e( 'Définir comme principal', 'smartseo-ai' ); ?>
                                    </button>
                                </div>
                                <div class="smartseo-ai-suggestion-details">
                                    <p><strong><?php esc_html_e( 'Pertinence', 'smartseo-ai' ); ?>:</strong> {{this.relevance}}</p>
                                    <p><strong><?php esc_html_e( 'Placement recommandé', 'smartseo-ai' ); ?>:</strong> {{this.placement}}</p>
                                </div>
                            </div>
                        {{/each}}
                    {{/if}}
                </div>
            </div>
        {{/if}}
        
        <!-- Suggestions de méta descriptions -->
        {{#if suggestions.meta}}
            <div class="smartseo-ai-accordion">
                <div class="smartseo-ai-accordion-header">
                    <span class="dashicons dashicons-editor-paragraph"></span>
                    <h3><?php esc_html_e( 'Méta descriptions optimisées', 'smartseo-ai' ); ?></h3>
                    <span class="smartseo-ai-accordion-toggle dashicons dashicons-arrow-down-alt2"></span>
                </div>
                
                <div class="smartseo-ai-accordion-content">
                    {{#if suggestions.meta.meta_descriptions.length}}
                        <p><?php esc_html_e( 'Voici des suggestions de méta descriptions optimisées pour votre contenu :', 'smartseo-ai' ); ?></p>
                        
                        {{#each suggestions.meta.meta_descriptions}}
                            <div class="smartseo-ai-suggestion-item">
                                <div class="smartseo-ai-suggestion-header">
                                    <div class="smartseo-ai-meta-count">{{this.character_count}} <?php esc_html_e( 'caractères', 'smartseo-ai' ); ?></div>
                                    <button class="button smartseo-ai-apply-suggestion" data-type="meta" data-id="{{this.id}}">
                                        <span class="dashicons dashicons-yes"></span>
                                        <?php esc_html_e( 'Appliquer', 'smartseo-ai' ); ?>
                                    </button>
                                </div>
                                <div class="smartseo-ai-suggestion-content">
                                    <div class="smartseo-ai-meta-description">{{this.description}}</div>
                                    {{#if this.includes_keyword}}
                                        <div class="smartseo-ai-meta-keyword-included">
                                            <span class="dashicons dashicons-yes-alt"></span>
                                            <?php esc_html_e( 'Inclut le mot-clé principal', 'smartseo-ai' ); ?>
                                        </div>
                                    {{else}}
                                        <div class="smartseo-ai-meta-keyword-missing">
                                            <span class="dashicons dashicons-warning"></span>
                                            <?php esc_html_e( 'N\'inclut pas le mot-clé principal', 'smartseo-ai' ); ?>
                                        </div>
                                    {{/if}}
                                </div>
                            </div>
                        {{/each}}
                    {{else}}
                        <p><?php esc_html_e( 'Aucune suggestion de méta description n\'a pu être générée.', 'smartseo-ai' ); ?></p>
                    {{/if}}
                </div>
            </div>
        {{/if}}
        
        <!-- Suggestions de titres et sous-titres -->
        {{#if suggestions.headings}}
            <div class="smartseo-ai-accordion">
                <div class="smartseo-ai-accordion-header">
                    <span class="dashicons dashicons-heading"></span>
                    <h3><?php esc_html_e( 'Titres et sous-titres optimisés', 'smartseo-ai' ); ?></h3>
                    <span class="smartseo-ai-accordion-toggle dashicons dashicons-arrow-down-alt2"></span>
                </div>
                
                <div class="smartseo-ai-accordion-content">
                    {{#if suggestions.headings.title_suggestion}}
                        <div class="smartseo-ai-suggestion-item">
                            <div class="smartseo-ai-suggestion-header">
                                <h4><?php esc_html_e( 'Suggestion de titre principal (H1)', 'smartseo-ai' ); ?></h4>
                                <button class="button smartseo-ai-apply-suggestion" data-type="headings" data-id="title">
                                    <span class="dashicons dashicons-yes"></span>
                                    <?php esc_html_e( 'Appliquer', 'smartseo-ai' ); ?>
                                </button>
                            </div>
                            <div class="smartseo-ai-suggestion-content">
                                <div class="smartseo-ai-heading">{{suggestions.headings.title_suggestion}}</div>
                            </div>
                        </div>
                    {{/if}}
                    
                    {{#if suggestions.headings.existing_headings_improvements.length}}
                        <h4><?php esc_html_e( 'Améliorations des titres existants', 'smartseo-ai' ); ?></h4>
                        {{#each suggestions.headings.existing_headings_improvements}}
                            <div class="smartseo-ai-suggestion-item">
                                <div class="smartseo-ai-suggestion-header">
                                    <div class="smartseo-ai-heading-level">H{{this.level}}</div>
                                    <button class="button smartseo-ai-apply-suggestion" data-type="headings" data-id="{{this.id}}">
                                        <span class="dashicons dashicons-yes"></span>
                                        <?php esc_html_e( 'Appliquer', 'smartseo-ai' ); ?>
                                    </button>
                                </div>
                                <div class="smartseo-ai-suggestion-content">
                                    <div class="smartseo-ai-heading-original">
                                        <strong><?php esc_html_e( 'Original', 'smartseo-ai' ); ?>:</strong> {{this.original}}
                                    </div>
                                    <div class="smartseo-ai-heading-improved">
                                        <strong><?php esc_html_e( 'Amélioré', 'smartseo-ai' ); ?>:</strong> {{this.improved}}
                                    </div>
                                    <div class="smartseo-ai-heading-reason">
                                        <strong><?php esc_html_e( 'Raison', 'smartseo-ai' ); ?>:</strong> {{this.reason}}
                                    </div>
                                </div>
                            </div>
                        {{/each}}
                    {{/if}}
                    
                    {{#if suggestions.headings.new_headings_suggestions.length}}
                        <h4><?php esc_html_e( 'Suggestions de nouveaux sous-titres', 'smartseo-ai' ); ?></h4>
                        {{#each suggestions.headings.new_headings_suggestions}}
                            <div class="smartseo-ai-suggestion-item">
                                <div class="smartseo-ai-suggestion-header">
                                    <div class="smartseo-ai-heading-level">H{{this.level}}</div>
                                    <button class="button smartseo-ai-copy-suggestion" data-content="{{this.heading}}">
                                        <span class="dashicons dashicons-clipboard"></span>
                                        <?php esc_html_e( 'Copier', 'smartseo-ai' ); ?>
                                    </button>
                                </div>
                                <div class="smartseo-ai-suggestion-content">
                                    <div class="smartseo-ai-heading">{{this.heading}}</div>
                                    <div class="smartseo-ai-heading-placement">
                                        <strong><?php esc_html_e( 'Placement suggéré', 'smartseo-ai' ); ?>:</strong> {{this.placement}}
                                    </div>
                                    <div class="smartseo-ai-heading-reason">
                                        <strong><?php esc_html_e( 'Raison', 'smartseo-ai' ); ?>:</strong> {{this.reason}}
                                    </div>
                                </div>
                            </div>
                        {{/each}}
                    {{/if}}
                </div>
            </div>
        {{/if}}
        
        <!-- Suggestions de structure de contenu -->
        {{#if suggestions.content_structure}}
            <div class="smartseo-ai-accordion">
                <div class="smartseo-ai-accordion-header">
                    <span class="dashicons dashicons-editor-table"></span>
                    <h3><?php esc_html_e( 'Structure du contenu', 'smartseo-ai' ); ?></h3>
                    <span class="smartseo-ai-accordion-toggle dashicons dashicons-arrow-down-alt2"></span>
                </div>
                
                <div class="smartseo-ai-accordion-content">
                    {{#if suggestions.content_structure.structure_issues.length}}
                        <h4><?php esc_html_e( 'Problèmes de structure identifiés', 'smartseo-ai' ); ?></h4>
                        {{#each suggestions.content_structure.structure_issues}}
                            <div class="smartseo-ai-suggestion-item">
                                <div class="smartseo-ai-suggestion-header">
                                    <div class="smartseo-ai-issue-type">{{this.type}}</div>
                                </div>
                                <div class="smartseo-ai-suggestion-content">
                                    <div class="smartseo-ai-issue-description">{{this.description}}</div>
                                    <div class="smartseo-ai-issue-location">
                                        <strong><?php esc_html_e( 'Emplacement', 'smartseo-ai' ); ?>:</strong> {{this.location}}
                                    </div>
                                    <div class="smartseo-ai-issue-suggestion">
                                        <strong><?php esc_html_e( 'Suggestion', 'smartseo-ai' ); ?>:</strong> {{this.suggestion}}
                                    </div>
                                </div>
                            </div>
                        {{/each}}
                    {{/if}}
                    
                    {{#if suggestions.content_structure.improvement_opportunities.length}}
                        <h4><?php esc_html_e( 'Opportunités d\'amélioration', 'smartseo-ai' ); ?></h4>
                        {{#each suggestions.content_structure.improvement_opportunities}}
                            <div class="smartseo-ai-suggestion-item">
                                <div class="smartseo-ai-suggestion-header">
                                    <div class="smartseo-ai-opportunity-type">{{this.type}}</div>
                                </div>
                                <div class="smartseo-ai-suggestion-content">
                                    <div class="smartseo-ai-opportunity-description">{{this.description}}</div>
                                    <div class="smartseo-ai-opportunity-location">
                                        <strong><?php esc_html_e( 'Emplacement', 'smartseo-ai' ); ?>:</strong> {{this.location}}
                                    </div>
                                    <div class="smartseo-ai-opportunity-suggestion">
                                        <strong><?php esc_html_e( 'Suggestion', 'smartseo-ai' ); ?>:</strong> {{this.suggestion}}
                                    </div>
                                </div>
                            </div>
                        {{/each}}
                    {{/if}}
                </div>
            </div>
        {{/if}}
        
        <!-- Suggestions de liens internes -->
        {{#if suggestions.internal_links}}
            <div class="smartseo-ai-accordion">
                <div class="smartseo-ai-accordion-header">
                    <span class="dashicons dashicons-admin-links"></span>
                    <h3><?php esc_html_e( 'Liens internes', 'smartseo-ai' ); ?></h3>
                    <span class="smartseo-ai-accordion-toggle dashicons dashicons-arrow-down-alt2"></span>
                </div>
                
                <div class="smartseo-ai-accordion-content">
                    {{#if suggestions.internal_links.link_suggestions.length}}
                        <h4><?php esc_html_e( 'Suggestions de liens internes', 'smartseo-ai' ); ?></h4>
                        {{#each suggestions.internal_links.link_suggestions}}
                            <div class="smartseo-ai-suggestion-item">
                                <div class="smartseo-ai-suggestion-header">
                                    <div class="smartseo-ai-link-target">{{this.target_post_title}}</div>
                                    <button class="button smartseo-ai-apply-suggestion" data-type="internal_links" data-id="{{this.id}}">
                                        <span class="dashicons dashicons-yes"></span>
                                        <?php esc_html_e( 'Appliquer', 'smartseo-ai' ); ?>
                                    </button>
                                </div>
                                <div class="smartseo-ai-suggestion-content">
                                    <div class="smartseo-ai-link-anchor">
                                        <strong><?php esc_html_e( 'Texte d\'ancrage', 'smartseo-ai' ); ?>:</strong> {{this.anchor_text}}
                                    </div>
                                    <div class="smartseo-ai-link-context">
                                        <strong><?php esc_html_e( 'Contexte', 'smartseo-ai' ); ?>:</strong> {{this.context}}
                                    </div>
                                    <div class="smartseo-ai-link-reason">
                                        <strong><?php esc_html_e( 'Raison', 'smartseo-ai' ); ?>:</strong> {{this.reason}}
                                    </div>
                                </div>
                            </div>
                        {{/each}}
                    {{else}}
                        <p><?php esc_html_e( 'Aucune suggestion de lien interne n\'a pu être générée.', 'smartseo-ai' ); ?></p>
                    {{/if}}
                </div>
            </div>
        {{/if}}
        
        <!-- Suggestions de balises alt pour les images -->
        {{#if suggestions.image_alt}}
            <div class="smartseo-ai-accordion">
                <div class="smartseo-ai-accordion-header">
                    <span class="dashicons dashicons-format-image"></span>
                    <h3><?php esc_html_e( 'Balises alt des images', 'smartseo-ai' ); ?></h3>
                    <span class="smartseo-ai-accordion-toggle dashicons dashicons-arrow-down-alt2"></span>
                </div>
                
                <div class="smartseo-ai-accordion-content">
                    {{#if suggestions.image_alt.alt_suggestions.length}}
                        <h4><?php esc_html_e( 'Suggestions de balises alt', 'smartseo-ai' ); ?></h4>
                        {{#each suggestions.image_alt.alt_suggestions}}
                            <div class="smartseo-ai-suggestion-item">
                                <div class="smartseo-ai-suggestion-header">
                                    <div class="smartseo-ai-image-preview">
                                        <img src="{{this.image_src}}" alt="" style="max-width: 100px; max-height: 60px;">
                                    </div>
                                    <button class="button smartseo-ai-apply-suggestion" data-type="image_alt" data-id="{{this.id}}">
                                        <span class="dashicons dashicons-yes"></span>
                                        <?php esc_html_e( 'Appliquer', 'smartseo-ai' ); ?>
                                    </button>
                                </div>
                                <div class="smartseo-ai-suggestion-content">
                                    <div class="smartseo-ai-alt-suggestion">
                                        <strong><?php esc_html_e( 'Alt suggéré', 'smartseo-ai' ); ?>:</strong> {{this.suggested_alt}}
                                    </div>
                                    <div class="smartseo-ai-alt-reason">
                                        <strong><?php esc_html_e( 'Raison', 'smartseo-ai' ); ?>:</strong> {{this.reason}}
                                    </div>
                                </div>
                            </div>
                        {{/each}}
                    {{else}}
                        <p><?php esc_html_e( 'Aucune suggestion de balise alt n\'a pu être générée.', 'smartseo-ai' ); ?></p>
                    {{/if}}
                </div>
            </div>
        {{/if}}
        
        <!-- Suggestions de lisibilité -->
        {{#if suggestions.readability}}
            <div class="smartseo-ai-accordion">
                <div class="smartseo-ai-accordion-header">
                    <span class="dashicons dashicons-book-alt"></span>
                    <h3><?php esc_html_e( 'Lisibilité', 'smartseo-ai' ); ?></h3>
                    <span class="smartseo-ai-accordion-toggle dashicons dashicons-arrow-down-alt2"></span>
                </div>
                
                <div class="smartseo-ai-accordion-content">
                    <div class="smartseo-ai-readability-score">
                        <div class="smartseo-ai-score-circle smartseo-ai-score-{{readability_class suggestions.readability.readability_score}}">
                            <div class="smartseo-ai-score-value">{{suggestions.readability.readability_score}}</div>
                        </div>
                        <div class="smartseo-ai-readability-level">
                            <strong><?php esc_html_e( 'Niveau de lisibilité', 'smartseo-ai' ); ?>:</strong> {{suggestions.readability.readability_level}}
                        </div>
                    </div>
                    
                    {{#if suggestions.readability.issues.length}}
                        <h4><?php esc_html_e( 'Problèmes de lisibilité', 'smartseo-ai' ); ?></h4>
                        {{#each suggestions.readability.issues}}
                            <div class="smartseo-ai-suggestion-item">
                                <div class="smartseo-ai-suggestion-header">
                                    <div class="smartseo-ai-issue-type">{{this.type}}</div>
                                </div>
                                <div class="smartseo-ai-suggestion-content">
                                    <div class="smartseo-ai-issue-description">{{this.description}}</div>
                                    <div class="smartseo-ai-issue-example">
                                        <strong><?php esc_html_e( 'Exemple', 'smartseo-ai' ); ?>:</strong> {{this.example}}
                                    </div>
                                    <div class="smartseo-ai-issue-suggestion">
                                        <strong><?php esc_html_e( 'Suggestion', 'smartseo-ai' ); ?>:</strong> {{this.suggestion}}
                                    </div>
                                </div>
                            </div>
                        {{/each}}
                    {{/if}}
                    
                    {{#if suggestions.readability.improvement_suggestions.length}}
                        <h4><?php esc_html_e( 'Suggestions d\'amélioration', 'smartseo-ai' ); ?></h4>
                        {{#each suggestions.readability.improvement_suggestions}}
                            <div class="smartseo-ai-suggestion-item">
                                <div class="smartseo-ai-suggestion-header">
                                    <div class="smartseo-ai-improvement-type">{{this.type}}</div>
                                </div>
                                <div class="smartseo-ai-suggestion-content">
                                    <div class="smartseo-ai-improvement-description">{{this.description}}</div>
                                    <div class="smartseo-ai-improvement-example">
                                        <strong><?php esc_html_e( 'Exemple', 'smartseo-ai' ); ?>:</strong> {{this.example}}
                                    </div>
                                    <div class="smartseo-ai-improvement-suggestion">
                                        <strong><?php esc_html_e( 'Suggestion', 'smartseo-ai' ); ?>:</strong> {{this.suggestion}}
                                    </div>
                                </div>
                            </div>
                        {{/each}}
                    {{/if}}
                </div>
            </div>
        {{/if}}
        
        <!-- Suggestions de rich snippets -->
        {{#if suggestions.rich_snippets}}
            <div class="smartseo-ai-accordion">
                <div class="smartseo-ai-accordion-header">
                    <span class="dashicons dashicons-code-standards"></span>
                    <h3><?php esc_html_e( 'Données structurées (Rich Snippets)', 'smartseo-ai' ); ?></h3>
                    <span class="smartseo-ai-accordion-toggle dashicons dashicons-arrow-down-alt2"></span>
                </div>
                
                <div class="smartseo-ai-accordion-content">
                    {{#if suggestions.rich_snippets.content_type}}
                        <div class="smartseo-ai-content-type">
                            <strong><?php esc_html_e( 'Type de contenu détecté', 'smartseo-ai' ); ?>:</strong> {{suggestions.rich_snippets.content_type}}
                        </div>
                    {{/if}}
                    
                    {{#if suggestions.rich_snippets.recommended_schemas.length}}
                        <h4><?php esc_html_e( 'Schémas recommandés', 'smartseo-ai' ); ?></h4>
                        {{#each suggestions.rich_snippets.recommended_schemas}}
                            <div class="smartseo-ai-suggestion-item">
                                <div class="smartseo-ai-suggestion-header">
                                    <div class="smartseo-ai-schema-type">{{this.schema_type}}</div>
                                </div>
                                <div class="smartseo-ai-suggestion-content">
                                    <div class="smartseo-ai-schema-relevance">
                                        <strong><?php esc_html_e( 'Pertinence', 'smartseo-ai' ); ?>:</strong> {{this.relevance}}
                                    </div>
                                    <div class="smartseo-ai-schema-description">
                                        <strong><?php esc_html_e( 'Description', 'smartseo-ai' ); ?>:</strong> {{this.description}}
                                    </div>
                                    <div class="smartseo-ai-schema-implementation">
                                        <strong><?php esc_html_e( 'Implémentation', 'smartseo-ai' ); ?>:</strong> {{this.implementation_suggestion}}
                                    </div>
                                </div>
                            </div>
                        {{/each}}
                    {{/if}}
                    
                    {{#if suggestions.rich_snippets.schema_json}}
                        <div class="smartseo-ai-suggestion-item">
                            <div class="smartseo-ai-suggestion-header">
                                <h4><?php esc_html_e( 'Code JSON-LD recommandé', 'smartseo-ai' ); ?></h4>
                                <button class="button smartseo-ai-apply-suggestion" data-type="rich_snippets" data-id="schema_json">
                                    <span class="dashicons dashicons-yes"></span>
                                    <?php esc_html_e( 'Appliquer', 'smartseo-ai' ); ?>
                                </button>
                            </div>
                            <div class="smartseo-ai-suggestion-content">
                                <pre class="smartseo-ai-schema-json">{{suggestions.rich_snippets.schema_json}}</pre>
                            </div>
                        </div>
                    {{/if}}
                </div>
            </div>
        {{/if}}
    </div>
</script>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Fonction pour copier du texte dans le presse-papiers
    $(document).on('click', '.smartseo-ai-copy-suggestion', function() {
        var content = $(this).data('content');
        var $temp = $('<textarea>');
        $('body').append($temp);
        $temp.val(content).select();
        document.execCommand('copy');
        $temp.remove();
        
        var $button = $(this);
        $button.html('<span class="dashicons dashicons-yes"></span> <?php esc_html_e( 'Copié !', 'smartseo-ai' ); ?>');
        
        setTimeout(function() {
            $button.html('<span class="dashicons dashicons-clipboard"></span> <?php esc_html_e( 'Copier', 'smartseo-ai' ); ?>');
        }, 2000);
    });
});
</script>
