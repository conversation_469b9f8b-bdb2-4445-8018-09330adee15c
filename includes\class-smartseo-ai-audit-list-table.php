<?php
/**
 * Classe pour le tableau des résultats d'audit SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Charger la classe WP_List_Table si elle n'est pas déjà chargée
if ( ! class_exists( 'WP_List_Table' ) ) {
    require_once ABSPATH . 'wp-admin/includes/class-wp-list-table.php';
}

/**
 * Classe qui gère l'affichage du tableau des résultats d'audit SEO
 */
class SmartSEO_AI_Audit_List_Table extends WP_List_Table {

    /**
     * Instance de la classe d'audit SEO en masse
     *
     * @var SmartSEO_AI_Bulk_Audit
     */
    private $bulk_audit;

    /**
     * Constructeur
     */
    public function __construct() {
        parent::__construct( array(
            'singular' => __( 'Résultat d\'audit', 'smartseo-ai' ),
            'plural'   => __( 'Résultats d\'audit', 'smartseo-ai' ),
            'ajax'     => false,
        ) );

        // Récupérer l'instance de la classe d'audit SEO en masse
        $this->bulk_audit = SmartSEO_AI_Bulk_Audit::get_instance();
    }

    /**
     * Récupère les données du tableau
     */
    public function prepare_items() {
        // Définir les colonnes
        $columns = $this->get_columns();
        $hidden = array();
        $sortable = $this->get_sortable_columns();
        $this->_column_headers = array( $columns, $hidden, $sortable );

        // Récupérer les résultats d'audit
        $results = $this->bulk_audit->get_audit_results();

        // Trier les résultats
        $orderby = isset( $_REQUEST['orderby'] ) ? sanitize_text_field( $_REQUEST['orderby'] ) : 'score';
        $order = isset( $_REQUEST['order'] ) ? sanitize_text_field( $_REQUEST['order'] ) : 'asc';

        usort( $results, function( $a, $b ) use ( $orderby, $order ) {
            if ( $orderby === 'score' ) {
                $result = $a['score'] - $b['score'];
            } elseif ( $orderby === 'issues' ) {
                $result = $a['issues'] - $b['issues'];
            } elseif ( $orderby === 'title' ) {
                $result = strcmp( $a['title'], $b['title'] );
            } elseif ( $orderby === 'post_type' ) {
                $result = strcmp( $a['post_type'], $b['post_type'] );
            } elseif ( $orderby === 'timestamp' ) {
                $result = strtotime( $a['timestamp'] ) - strtotime( $b['timestamp'] );
            } else {
                $result = 0;
            }

            return $order === 'asc' ? $result : -$result;
        } );

        // Paginer les résultats
        $per_page = 20;
        $current_page = $this->get_pagenum();
        $total_items = count( $results );

        $this->set_pagination_args( array(
            'total_items' => $total_items,
            'per_page'    => $per_page,
            'total_pages' => ceil( $total_items / $per_page ),
        ) );

        $this->items = array_slice( $results, ( ( $current_page - 1 ) * $per_page ), $per_page );
    }

    /**
     * Définit les colonnes du tableau
     *
     * @return array Colonnes du tableau.
     */
    public function get_columns() {
        return array(
            'cb'        => '<input type="checkbox" />',
            'title'     => __( 'Titre', 'smartseo-ai' ),
            'url'       => __( 'URL', 'smartseo-ai' ),
            'post_type' => __( 'Type', 'smartseo-ai' ),
            'score'     => __( 'Score SEO', 'smartseo-ai' ),
            'issues'    => __( 'Problèmes', 'smartseo-ai' ),
            'timestamp' => __( 'Date d\'audit', 'smartseo-ai' ),
            'actions'   => __( 'Actions', 'smartseo-ai' ),
        );
    }

    /**
     * Définit les colonnes triables du tableau
     *
     * @return array Colonnes triables du tableau.
     */
    public function get_sortable_columns() {
        return array(
            'title'     => array( 'title', false ),
            'post_type' => array( 'post_type', false ),
            'score'     => array( 'score', true ),
            'issues'    => array( 'issues', false ),
            'timestamp' => array( 'timestamp', false ),
        );
    }

    /**
     * Définit les actions en masse
     *
     * @return array Actions en masse.
     */
    public function get_bulk_actions() {
        return array(
            'export_csv' => __( 'Exporter en CSV', 'smartseo-ai' ),
            'export_pdf' => __( 'Exporter en PDF', 'smartseo-ai' ),
            'reaudit'    => __( 'Réexécuter l\'audit', 'smartseo-ai' ),
        );
    }

    /**
     * Affiche la case à cocher pour les actions en masse
     *
     * @param array $item Élément du tableau.
     * @return string Case à cocher HTML.
     */
    public function column_cb( $item ) {
        return sprintf(
            '<input type="checkbox" name="audit_ids[]" value="%s" />',
            $item['post_id']
        );
    }

    /**
     * Affiche la colonne titre
     *
     * @param array $item Élément du tableau.
     * @return string Contenu de la colonne.
     */
    public function column_title( $item ) {
        $title = '<strong>' . esc_html( $item['title'] ) . '</strong>';
        
        // Ajouter les actions en ligne
        $actions = array(
            'view'    => sprintf(
                '<a href="%s" target="_blank">%s</a>',
                esc_url( get_permalink( $item['post_id'] ) ),
                __( 'Voir', 'smartseo-ai' )
            ),
            'edit'    => sprintf(
                '<a href="%s">%s</a>',
                esc_url( get_edit_post_link( $item['post_id'] ) ),
                __( 'Modifier', 'smartseo-ai' )
            ),
            'reaudit' => sprintf(
                '<a href="#" class="smartseo-ai-reaudit" data-post-id="%s">%s</a>',
                esc_attr( $item['post_id'] ),
                __( 'Réexécuter l\'audit', 'smartseo-ai' )
            ),
        );

        return $title . $this->row_actions( $actions );
    }

    /**
     * Affiche la colonne URL
     *
     * @param array $item Élément du tableau.
     * @return string Contenu de la colonne.
     */
    public function column_url( $item ) {
        return sprintf(
            '<a href="%s" target="_blank">%s</a>',
            esc_url( $item['url'] ),
            esc_url( $item['url'] )
        );
    }

    /**
     * Affiche la colonne type
     *
     * @param array $item Élément du tableau.
     * @return string Contenu de la colonne.
     */
    public function column_post_type( $item ) {
        $post_type_object = get_post_type_object( $item['post_type'] );
        return $post_type_object ? esc_html( $post_type_object->labels->singular_name ) : esc_html( $item['post_type'] );
    }

    /**
     * Affiche la colonne score
     *
     * @param array $item Élément du tableau.
     * @return string Contenu de la colonne.
     */
    public function column_score( $item ) {
        $score = intval( $item['score'] );
        $class = '';
        
        if ( $score >= 80 ) {
            $class = 'smartseo-ai-score-good';
        } elseif ( $score >= 50 ) {
            $class = 'smartseo-ai-score-average';
        } else {
            $class = 'smartseo-ai-score-poor';
        }
        
        return sprintf(
            '<div class="smartseo-ai-score %s">%d</div>',
            esc_attr( $class ),
            $score
        );
    }

    /**
     * Affiche la colonne problèmes
     *
     * @param array $item Élément du tableau.
     * @return string Contenu de la colonne.
     */
    public function column_issues( $item ) {
        $issues = intval( $item['issues'] );
        $class = '';
        
        if ( $issues === 0 ) {
            $class = 'smartseo-ai-issues-none';
        } elseif ( $issues <= 5 ) {
            $class = 'smartseo-ai-issues-few';
        } else {
            $class = 'smartseo-ai-issues-many';
        }
        
        return sprintf(
            '<div class="smartseo-ai-issues %s">%d</div>',
            esc_attr( $class ),
            $issues
        );
    }

    /**
     * Affiche la colonne date d'audit
     *
     * @param array $item Élément du tableau.
     * @return string Contenu de la colonne.
     */
    public function column_timestamp( $item ) {
        return esc_html( $item['timestamp'] );
    }

    /**
     * Affiche la colonne actions
     *
     * @param array $item Élément du tableau.
     * @return string Contenu de la colonne.
     */
    public function column_actions( $item ) {
        $actions = array();
        
        // Bouton pour voir le rapport détaillé
        $actions[] = sprintf(
            '<a href="%s" class="button button-small smartseo-ai-view-report" data-post-id="%s">%s</a>',
            esc_url( admin_url( 'admin.php?page=smartseo-ai-audit&post_id=' . $item['post_id'] ) ),
            esc_attr( $item['post_id'] ),
            __( 'Voir le rapport', 'smartseo-ai' )
        );
        
        // Bouton pour corriger automatiquement
        if ( $item['issues'] > 0 ) {
            $actions[] = sprintf(
                '<a href="#" class="button button-small smartseo-ai-auto-fix" data-post-id="%s">%s</a>',
                esc_attr( $item['post_id'] ),
                __( 'Corriger auto.', 'smartseo-ai' )
            );
        }
        
        return implode( ' ', $actions );
    }

    /**
     * Affiche un message si aucun élément n'est trouvé
     */
    public function no_items() {
        esc_html_e( 'Aucun résultat d\'audit trouvé.', 'smartseo-ai' );
    }
}
