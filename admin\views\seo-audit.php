<?php
/**
 * Vue pour la page d'audit SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap smartseo-ai-wrap">
    <h1><?php esc_html_e( 'Audit SEO', 'smartseo-ai' ); ?></h1>

    <div class="smartseo-ai-audit-container">
        <div class="smartseo-ai-audit-form">
            <div class="smartseo-ai-card">
                <h2><?php esc_html_e( 'Lancer un audit SEO', 'smartseo-ai' ); ?></h2>
                <p><?php esc_html_e( 'Analysez une page ou un article de votre site pour obtenir un rapport SEO complet.', 'smartseo-ai' ); ?></p>

                <div class="smartseo-ai-tabs">
                    <div class="smartseo-ai-tab-nav">
                        <button class="smartseo-ai-tab-button active" data-tab="post"><?php esc_html_e( 'Article/Page', 'smartseo-ai' ); ?></button>
                        <button class="smartseo-ai-tab-button" data-tab="url"><?php esc_html_e( 'URL', 'smartseo-ai' ); ?></button>
                    </div>

                    <div class="smartseo-ai-tab-content active" id="post-tab">
                        <form id="smartseo-ai-audit-post-form">
                            <div class="smartseo-ai-form-group">
                                <label for="post-id"><?php esc_html_e( 'Sélectionnez un article ou une page', 'smartseo-ai' ); ?></label>
                                <select id="post-id" name="post_id" class="smartseo-ai-select2">
                                    <option value=""><?php esc_html_e( 'Sélectionnez un article ou une page', 'smartseo-ai' ); ?></option>
                                    <?php
                                    $posts = get_posts( array(
                                        'post_type'      => array( 'post', 'page' ),
                                        'post_status'    => 'publish',
                                        'posts_per_page' => 100,
                                        'orderby'        => 'date',
                                        'order'          => 'DESC',
                                    ) );

                                    foreach ( $posts as $post ) {
                                        printf(
                                            '<option value="%d">%s (%s)</option>',
                                            esc_attr( $post->ID ),
                                            esc_html( $post->post_title ),
                                            esc_html( get_post_type_object( $post->post_type )->labels->singular_name )
                                        );
                                    }
                                    ?>
                                </select>
                            </div>

                            <div class="smartseo-ai-form-actions">
                                <button type="submit" class="button button-primary"><?php esc_html_e( 'Lancer l\'audit', 'smartseo-ai' ); ?></button>
                            </div>
                        </form>
                    </div>

                    <div class="smartseo-ai-tab-content" id="url-tab">
                        <form id="smartseo-ai-audit-url-form">
                            <div class="smartseo-ai-form-group">
                                <label for="url"><?php esc_html_e( 'URL à analyser', 'smartseo-ai' ); ?></label>
                                <input type="url" id="url" name="url" class="regular-text" placeholder="https://example.com/page" required>
                            </div>

                            <div class="smartseo-ai-form-actions">
                                <button type="submit" class="button button-primary"><?php esc_html_e( 'Lancer l\'audit', 'smartseo-ai' ); ?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="smartseo-ai-audit-results" style="display: none;">
            <div class="smartseo-ai-card">
                <div class="smartseo-ai-audit-loading">
                    <div class="smartseo-ai-spinner"></div>
                    <p><?php esc_html_e( 'Analyse en cours...', 'smartseo-ai' ); ?></p>
                </div>

                <div class="smartseo-ai-audit-report" style="display: none;">
                    <!-- Le rapport sera inséré ici via JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Templates pour le rapport -->
<?php include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/partials/audit-report-template.php'; ?>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Initialiser Select2
    $('.smartseo-ai-select2').select2({
        width: '100%',
        placeholder: '<?php esc_attr_e( 'Sélectionnez un article ou une page', 'smartseo-ai' ); ?>',
        allowClear: true
    });

    // Gestion des onglets
    $('.smartseo-ai-tab-button').on('click', function() {
        var tab = $(this).data('tab');

        // Activer l'onglet
        $('.smartseo-ai-tab-button').removeClass('active');
        $(this).addClass('active');

        // Afficher le contenu de l'onglet
        $('.smartseo-ai-tab-content').removeClass('active');
        $('#' + tab + '-tab').addClass('active');
    });

    // Soumission du formulaire d'audit (article/page)
    $('#smartseo-ai-audit-post-form').on('submit', function(e) {
        e.preventDefault();

        var postId = $('#post-id').val();

        if (!postId) {
            alert('<?php esc_attr_e( 'Veuillez sélectionner un article ou une page.', 'smartseo-ai' ); ?>');
            return;
        }

        runAudit({ post_id: postId });
    });

    // Soumission du formulaire d'audit (URL)
    $('#smartseo-ai-audit-url-form').on('submit', function(e) {
        e.preventDefault();

        var url = $('#url').val();

        if (!url) {
            alert('<?php esc_attr_e( 'Veuillez saisir une URL valide.', 'smartseo-ai' ); ?>');
            return;
        }

        runAudit({ url: url });
    });

    // Fonction pour lancer l'audit
    function runAudit(data) {
        // Afficher la section des résultats
        $('.smartseo-ai-audit-results').show();
        $('.smartseo-ai-audit-loading').show();
        $('.smartseo-ai-audit-report').hide();

        // Faire défiler jusqu'aux résultats
        $('html, body').animate({
            scrollTop: $('.smartseo-ai-audit-results').offset().top - 50
        }, 500);

        // Ajouter le nonce
        data.nonce = '<?php echo wp_create_nonce( 'smartseo_ai_nonce' ); ?>';
        data.action = 'smartseo_ai_run_audit';

        // Envoyer la requête AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    renderAuditReport(response.data.results);
                } else {
                    alert(response.data.message || '<?php esc_attr_e( 'Une erreur s\'est produite lors de l\'audit.', 'smartseo-ai' ); ?>');
                    $('.smartseo-ai-audit-results').hide();
                }
            },
            error: function() {
                alert('<?php esc_attr_e( 'Une erreur s\'est produite lors de la communication avec le serveur.', 'smartseo-ai' ); ?>');
                $('.smartseo-ai-audit-results').hide();
            },
            complete: function() {
                $('.smartseo-ai-audit-loading').hide();
            }
        });
    }

    // Fonction pour afficher le rapport d'audit
    function renderAuditReport(results) {
        // Utiliser le template Handlebars pour générer le rapport
        var source = $('#audit-report-template').html();
        var template = Handlebars.compile(source);
        var html = template(results.report);

        // Insérer le rapport dans la page
        $('.smartseo-ai-audit-report').html(html).show();

        // Initialiser les accordéons
        $('.smartseo-ai-accordion-header').on('click', function() {
            $(this).toggleClass('active');
            $(this).next('.smartseo-ai-accordion-content').slideToggle(200);
        });
    }

    // Helpers Handlebars
    Handlebars.registerHelper('scoreClass', function(score) {
        if (score >= 80) {
            return 'good';
        } else if (score >= 50) {
            return 'average';
        } else {
            return 'poor';
        }
    });

    Handlebars.registerHelper('statusClass', function(status) {
        switch (status) {
            case 'good':
                return 'success';
            case 'warning':
                return 'warning';
            case 'critical':
                return 'error';
            case 'info':
                return 'info';
            default:
                return '';
        }
    });

    Handlebars.registerHelper('statusIcon', function(status) {
        switch (status) {
            case 'good':
                return 'yes';
            case 'warning':
                return 'warning';
            case 'critical':
                return 'no';
            case 'info':
                return 'info';
            default:
                return 'info';
        }
    });

    // Helper pour joindre les éléments d'un tableau
    Handlebars.registerHelper('joinArray', function(array, separator) {
        if (!array || !Array.isArray(array)) {
            return '';
        }
        return array.join(separator || ', ');
    });
});
</script>
