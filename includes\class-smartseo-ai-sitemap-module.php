<?php
/**
 * Classe principale du module Sitemap XML
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui centralise tous les modules du Sitemap XML
 */
class SmartSEO_AI_Sitemap_Module {

    /**
     * Instance de la classe
     *
     * @var SmartSEO_AI_Sitemap_Module
     */
    private static $instance = null;

    /**
     * Constructeur
     */
    private function __construct() {
        // Charger les dépendances
        $this->load_dependencies();

        // Initialiser les modules
        $this->init_modules();

        // Ajouter les hooks
        $this->add_hooks();
    }

    /**
     * Récupère l'instance de la classe
     *
     * @return SmartSEO_AI_Sitemap_Module Instance de la classe.
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Charge les dépendances
     */
    private function load_dependencies() {
        // Charger les classes principales
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/class-smartseo-ai-sitemap.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/class-smartseo-ai-sitemap-generator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/class-smartseo-ai-sitemap-settings.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/class-smartseo-ai-sitemap-ajax.php';

        // Charger les classes de paramètres
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/settings/class-smartseo-ai-sitemap-settings-ui.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/settings/class-smartseo-ai-sitemap-settings-validator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/settings/class-smartseo-ai-sitemap-settings-saver.php';
    }

    /**
     * Initialise les modules
     */
    private function init_modules() {
        // Initialiser les classes
        SmartSEO_AI_Sitemap::get_instance();
        SmartSEO_AI_Sitemap_Settings::get_instance();
        SmartSEO_AI_Sitemap_Ajax::get_instance();
    }

    /**
     * Ajoute les hooks
     */
    private function add_hooks() {
        // Ajouter les styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_styles' ) );

        // Ajouter les actions planifiées
        add_action( 'smartseo_ai_generate_sitemap', array( $this, 'generate_sitemap' ) );
    }

    /**
     * Enqueue les styles
     *
     * @param string $hook Hook de la page.
     */
    public function enqueue_styles( $hook ) {
        // Vérifier si nous sommes sur la page des paramètres
        if ( 'toplevel_page_smartseo-ai-settings' !== $hook && 'settings_page_smartseo-ai-settings' !== $hook ) {
            return;
        }

        // Enregistrer le style
        wp_enqueue_style(
            'smartseo-ai-sitemap',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/sitemap/smartseo-ai-sitemap.css',
            array(),
            SMARTSEO_AI_VERSION
        );
    }

    /**
     * Génère le sitemap
     */
    public function generate_sitemap() {
        // Récupérer les options du sitemap
        $options = get_option( 'smartseo_ai_sitemap_options', array() );

        // Vérifier si le sitemap est activé
        if ( isset( $options['enabled'] ) && 'yes' === $options['enabled'] ) {
            // Instancier le générateur de sitemap
            $generator = new SmartSEO_AI_Sitemap_Generator( $options );

            // Générer le sitemap d'index
            $generator->generate_index_sitemap();

            // Générer les sitemaps spécifiques
            $generator->generate_sitemap( 'post' );
            $generator->generate_sitemap( 'page' );

            // Générer les autres sitemaps en fonction des options
            if ( isset( $options['include_products'] ) && 'yes' === $options['include_products'] && class_exists( 'WooCommerce' ) ) {
                $generator->generate_sitemap( 'product' );
            }

            if ( isset( $options['include_categories'] ) && 'yes' === $options['include_categories'] ) {
                $generator->generate_sitemap( 'category' );
            }

            if ( isset( $options['include_tags'] ) && 'yes' === $options['include_tags'] ) {
                $generator->generate_sitemap( 'post_tag' );
            }
        }
    }
}
