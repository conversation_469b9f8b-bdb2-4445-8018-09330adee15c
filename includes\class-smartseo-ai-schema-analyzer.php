<?php
/**
 * Classe pour analyser les données structurées (Schema.org)
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui analyse les données structurées (Schema.org)
 */
class SmartSEO_AI_Schema_Analyzer {

    /**
     * Constructeur
     */
    public function __construct() {
        // Rien à initialiser pour l'instant
    }

    /**
     * Analyse les données structurées d'un article
     *
     * @param int $post_id ID de l'article à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_post( $post_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return array(
                'status' => 'error',
                'message' => __( 'Article introuvable.', 'smartseo-ai' ),
            );
        }

        // Récupérer l'URL de l'article
        $url = get_permalink( $post_id );

        // Analyser les données structurées de l'URL
        return $this->analyze_url( $url );
    }

    /**
     * Analyse les données structurées d'une URL
     *
     * @param string $url URL à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_url( $url ) {
        // Récupérer le contenu de la page
        $response = wp_remote_get( $url );
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }

        $html = wp_remote_retrieve_body( $response );
        if ( empty( $html ) ) {
            return array(
                'status' => 'error',
                'message' => __( 'Impossible de récupérer le contenu de la page.', 'smartseo-ai' ),
            );
        }

        // Extraire les données structurées
        $schema_data = $this->extract_schema_data( $html );

        // Analyser les données structurées
        return $this->analyze_schema_data( $schema_data );
    }

    /**
     * Extrait les données structurées du HTML
     *
     * @param string $html Contenu HTML à analyser.
     * @return array Données structurées extraites.
     */
    private function extract_schema_data( $html ) {
        $schema_data = array();

        // Rechercher les balises script de type application/ld+json
        preg_match_all( '/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/s', $html, $matches );

        if ( ! empty( $matches[1] ) ) {
            foreach ( $matches[1] as $json ) {
                $data = json_decode( $json, true );
                if ( $data ) {
                    $schema_data[] = $data;
                }
            }
        }

        // Rechercher les attributs itemscope et itemtype
        $doc = new DOMDocument();
        @$doc->loadHTML( mb_convert_encoding( $html, 'HTML-ENTITIES', 'UTF-8' ) );
        $xpath = new DOMXPath( $doc );

        $microdata_nodes = $xpath->query( '//*[@itemscope and @itemtype]' );
        if ( $microdata_nodes->length > 0 ) {
            foreach ( $microdata_nodes as $node ) {
                $itemtype = $node->getAttribute( 'itemtype' );
                $schema_data[] = array(
                    '@type' => basename( $itemtype ),
                    '@context' => 'http://schema.org',
                    '_format' => 'microdata',
                );
            }
        }

        return $schema_data;
    }

    /**
     * Analyse les données structurées
     *
     * @param array $schema_data Données structurées à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_schema_data( $schema_data ) {
        $results = array(
            'status' => 'success',
            'has_schema' => ! empty( $schema_data ),
            'schema_count' => count( $schema_data ),
            'schema_types' => array(),
            'schema_data' => $schema_data,
            'issues' => array(),
            'recommendations' => array(),
            'score' => 0,
        );

        if ( empty( $schema_data ) ) {
            $results['issues'][] = __( 'Aucune donnée structurée (Schema.org) n\'a été trouvée.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Ajoutez des données structurées pour améliorer la visibilité dans les résultats de recherche.', 'smartseo-ai' );
            return $results;
        }

        // Identifier les types de schéma
        foreach ( $schema_data as $data ) {
            if ( isset( $data['@type'] ) ) {
                $type = $data['@type'];
                if ( is_array( $type ) ) {
                    foreach ( $type as $t ) {
                        $results['schema_types'][] = $t;
                    }
                } else {
                    $results['schema_types'][] = $type;
                }
            }
        }

        $results['schema_types'] = array_unique( $results['schema_types'] );

        // Vérifier les types de schéma courants
        $common_types = array(
            'Article',
            'BlogPosting',
            'WebPage',
            'Product',
            'Organization',
            'Person',
            'LocalBusiness',
            'BreadcrumbList',
            'FAQPage',
            'HowTo',
            'Recipe',
            'Review',
            'Event',
        );

        $has_common_type = false;
        foreach ( $common_types as $type ) {
            if ( in_array( $type, $results['schema_types'], true ) ) {
                $has_common_type = true;
                break;
            }
        }

        if ( ! $has_common_type ) {
            $results['issues'][] = __( 'Aucun type de schéma courant n\'a été trouvé.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Ajoutez des types de schéma courants comme Article, Product, Organization, etc.', 'smartseo-ai' );
        }

        // Vérifier les propriétés obligatoires pour chaque type de schéma
        foreach ( $schema_data as $data ) {
            if ( isset( $data['@type'] ) ) {
                $type = $data['@type'];
                if ( is_array( $type ) ) {
                    foreach ( $type as $t ) {
                        $this->check_required_properties( $t, $data, $results );
                    }
                } else {
                    $this->check_required_properties( $type, $data, $results );
                }
            }
        }

        // Calculer le score
        $score = 0;
        if ( $results['has_schema'] ) {
            $score += 50;
            if ( $has_common_type ) {
                $score += 30;
            }
            if ( count( $results['issues'] ) === 0 ) {
                $score += 20;
            } else {
                $score -= count( $results['issues'] ) * 5;
            }
        }

        $results['score'] = max( 0, $score );

        return $results;
    }

    /**
     * Vérifie les propriétés obligatoires pour un type de schéma
     *
     * @param string $type    Type de schéma.
     * @param array  $data    Données du schéma.
     * @param array  $results Résultats de l'analyse.
     */
    private function check_required_properties( $type, $data, &$results ) {
        $required_properties = array();

        switch ( $type ) {
            case 'Article':
            case 'BlogPosting':
                $required_properties = array( 'headline', 'author', 'datePublished', 'image' );
                break;

            case 'Product':
                $required_properties = array( 'name', 'image', 'description', 'offers' );
                break;

            case 'Organization':
                $required_properties = array( 'name', 'url', 'logo' );
                break;

            case 'Person':
                $required_properties = array( 'name' );
                break;

            case 'LocalBusiness':
                $required_properties = array( 'name', 'address', 'telephone' );
                break;

            case 'BreadcrumbList':
                $required_properties = array( 'itemListElement' );
                break;

            case 'FAQPage':
                $required_properties = array( 'mainEntity' );
                break;

            case 'HowTo':
                $required_properties = array( 'name', 'step' );
                break;

            case 'Recipe':
                $required_properties = array( 'name', 'recipeIngredient', 'recipeInstructions' );
                break;

            case 'Review':
                $required_properties = array( 'itemReviewed', 'reviewRating' );
                break;

            case 'Event':
                $required_properties = array( 'name', 'startDate', 'location' );
                break;
        }

        if ( ! empty( $required_properties ) ) {
            $missing_properties = array();
            foreach ( $required_properties as $property ) {
                if ( ! isset( $data[$property] ) || empty( $data[$property] ) ) {
                    $missing_properties[] = $property;
                }
            }

            if ( ! empty( $missing_properties ) ) {
                $results['issues'][] = sprintf(
                    __( 'Le schéma de type "%s" manque de propriétés obligatoires : %s.', 'smartseo-ai' ),
                    $type,
                    implode( ', ', $missing_properties )
                );
                $results['recommendations'][] = sprintf(
                    __( 'Ajoutez les propriétés manquantes au schéma de type "%s".', 'smartseo-ai' ),
                    $type
                );
            }
        }
    }
}
