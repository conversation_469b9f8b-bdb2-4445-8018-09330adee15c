<?php
/**
 * Classe des paramètres du Sitemap XML
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère les paramètres du Sitemap XML
 */
class SmartSEO_AI_Sitemap_Settings {

    /**
     * Instance de la classe
     *
     * @var SmartSEO_AI_Sitemap_Settings
     */
    private static $instance = null;

    /**
     * Options du sitemap
     *
     * @var array
     */
    private $options;

    /**
     * Constructeur
     */
    private function __construct() {
        // Charger les options
        $this->options = get_option( 'smartseo_ai_sitemap_options', array(
            'enabled' => 'no',
            'include_posts' => 'yes',
            'include_pages' => 'yes',
            'include_products' => 'yes',
            'include_categories' => 'yes',
            'include_tags' => 'yes',
            'include_custom_taxonomies' => 'yes',
            'include_custom_post_types' => 'yes',
            'include_media' => 'no',
            'use_index' => 'no',
            'max_entries' => 1000,
            'frequencies' => array(
                'post' => 'weekly',
                'page' => 'monthly',
                'product' => 'daily',
                'category' => 'monthly',
                'post_tag' => 'monthly',
                'attachment' => 'yearly',
            ),
            'priorities' => array(
                'post' => 0.7,
                'page' => 0.8,
                'product' => 0.9,
                'category' => 0.6,
                'post_tag' => 0.5,
                'attachment' => 0.3,
            ),
        ) );

        // Charger les dépendances
        $this->load_dependencies();

        // Ajouter les hooks
        $this->add_hooks();
    }

    /**
     * Récupère l'instance de la classe
     *
     * @return SmartSEO_AI_Sitemap_Settings Instance de la classe.
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Charge les dépendances
     */
    private function load_dependencies() {
        // Charger les classes nécessaires
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/settings/class-smartseo-ai-sitemap-settings-ui.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/settings/class-smartseo-ai-sitemap-settings-validator.php';
        require_once SMARTSEO_AI_PLUGIN_DIR . 'includes/sitemap/settings/class-smartseo-ai-sitemap-settings-saver.php';
    }

    /**
     * Ajoute les hooks
     */
    private function add_hooks() {
        // Ajouter l'onglet des paramètres
        add_filter( 'smartseo_ai_settings_tabs', array( $this, 'add_settings_tab' ) );

        // Ajouter la section des paramètres
        add_action( 'smartseo_ai_settings_content_sitemap', array( $this, 'render_settings_content' ) );

        // Enregistrer les paramètres
        add_action( 'admin_init', array( $this, 'register_settings' ) );
    }

    /**
     * Ajoute l'onglet des paramètres
     *
     * @param array $tabs Onglets existants.
     * @return array Onglets modifiés.
     */
    public function add_settings_tab( $tabs ) {
        $tabs['sitemap'] = array(
            'title' => __( 'Sitemap XML', 'smartseo-ai' ),
            'icon'  => 'dashicons-networking',
        );

        return $tabs;
    }

    /**
     * Affiche le contenu des paramètres
     */
    public function render_settings_content() {
        // Instancier la classe d'interface utilisateur
        $ui = new SmartSEO_AI_Sitemap_Settings_UI( $this->options );

        // Afficher le formulaire
        $ui->render_settings_form();
    }

    /**
     * Enregistre les paramètres
     */
    public function register_settings() {
        register_setting(
            'smartseo-ai-settings',
            'smartseo_ai_sitemap_options',
            array(
                'sanitize_callback' => array( $this, 'sanitize_settings' ),
                'default'           => array(
                    'enabled' => 'no',
                    'include_posts' => 'yes',
                    'include_pages' => 'yes',
                    'include_products' => 'yes',
                    'include_categories' => 'yes',
                    'include_tags' => 'yes',
                    'include_custom_taxonomies' => 'yes',
                    'include_custom_post_types' => 'yes',
                    'include_media' => 'no',
                    'use_index' => 'no',
                    'max_entries' => 1000,
                    'frequencies' => array(
                        'post' => 'weekly',
                        'page' => 'monthly',
                        'product' => 'daily',
                        'category' => 'monthly',
                        'post_tag' => 'monthly',
                        'attachment' => 'yearly',
                    ),
                    'priorities' => array(
                        'post' => 0.7,
                        'page' => 0.8,
                        'product' => 0.9,
                        'category' => 0.6,
                        'post_tag' => 0.5,
                        'attachment' => 0.3,
                    ),
                ),
            )
        );
    }

    /**
     * Sanitize les paramètres
     *
     * @param array $input Paramètres à sanitizer.
     * @return array Paramètres sanitizés.
     */
    public function sanitize_settings( $input ) {
        // Instancier la classe de validation
        $validator = new SmartSEO_AI_Sitemap_Settings_Validator();

        // Valider les paramètres
        $validated = $validator->validate( $input );

        // Instancier la classe de sauvegarde
        $saver = new SmartSEO_AI_Sitemap_Settings_Saver();

        // Sauvegarder les paramètres
        return $saver->save( $validated );
    }
}
