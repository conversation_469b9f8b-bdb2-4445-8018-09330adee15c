<?php
/**
 * Vue pour la page d'audit SEO en masse
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Initialiser le tableau des résultats
$audit_table = new SmartSEO_AI_Audit_List_Table();
$audit_table->prepare_items();

// Récupérer les types de publication
$post_types = get_post_types( array( 'public' => true ), 'objects' );
?>

<div class="wrap smartseo-ai-wrap">
    <h1><?php esc_html_e( 'Audit SEO en masse', 'smartseo-ai' ); ?></h1>
    
    <div class="smartseo-ai-bulk-audit-container">
        <div class="smartseo-ai-bulk-audit-form smartseo-ai-card">
            <h2><?php esc_html_e( 'Lancer un audit SEO en masse', 'smartseo-ai' ); ?></h2>
            <p><?php esc_html_e( 'Analysez plusieurs pages de votre site pour obtenir un rapport SEO complet.', 'smartseo-ai' ); ?></p>
            
            <form id="smartseo-ai-bulk-audit-form">
                <div class="smartseo-ai-form-group">
                    <label><?php esc_html_e( 'Types de publication à auditer', 'smartseo-ai' ); ?></label>
                    <div class="smartseo-ai-checkbox-group">
                        <?php foreach ( $post_types as $post_type ) : ?>
                            <label>
                                <input type="checkbox" name="post_types[]" value="<?php echo esc_attr( $post_type->name ); ?>" <?php checked( in_array( $post_type->name, array( 'post', 'page' ) ) ); ?>>
                                <?php echo esc_html( $post_type->labels->name ); ?>
                            </label>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="smartseo-ai-form-group">
                    <label for="limit"><?php esc_html_e( 'Nombre maximum de pages à auditer', 'smartseo-ai' ); ?></label>
                    <select id="limit" name="limit" class="smartseo-ai-select">
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                        <option value="500">500</option>
                        <option value="1000">1000</option>
                    </select>
                    <p class="description"><?php esc_html_e( 'Un nombre élevé peut prendre plus de temps.', 'smartseo-ai' ); ?></p>
                </div>
                
                <div class="smartseo-ai-form-actions">
                    <button type="submit" class="button button-primary" id="smartseo-ai-run-audit">
                        <span class="dashicons dashicons-chart-bar"></span>
                        <?php esc_html_e( 'Lancer l\'audit', 'smartseo-ai' ); ?>
                    </button>
                </div>
            </form>
        </div>
        
        <div class="smartseo-ai-bulk-audit-progress smartseo-ai-card" style="display: none;">
            <h2><?php esc_html_e( 'Progression de l\'audit', 'smartseo-ai' ); ?></h2>
            
            <div class="smartseo-ai-progress-container">
                <div class="smartseo-ai-progress-bar">
                    <div class="smartseo-ai-progress-value" style="width: 0%;"></div>
                </div>
                <div class="smartseo-ai-progress-text">
                    <span class="smartseo-ai-progress-percent">0%</span>
                    <span class="smartseo-ai-progress-count">0 / 0</span>
                </div>
            </div>
            
            <div class="smartseo-ai-progress-status">
                <?php esc_html_e( 'Préparation de l\'audit...', 'smartseo-ai' ); ?>
            </div>
            
            <div class="smartseo-ai-progress-actions">
                <button type="button" class="button button-secondary" id="smartseo-ai-cancel-audit">
                    <?php esc_html_e( 'Annuler', 'smartseo-ai' ); ?>
                </button>
            </div>
        </div>
        
        <div class="smartseo-ai-bulk-audit-results smartseo-ai-card">
            <h2><?php esc_html_e( 'Résultats de l\'audit SEO', 'smartseo-ai' ); ?></h2>
            
            <div class="smartseo-ai-bulk-audit-actions">
                <div class="smartseo-ai-bulk-audit-filters">
                    <select id="smartseo-ai-filter-post-type" class="smartseo-ai-select">
                        <option value=""><?php esc_html_e( 'Tous les types', 'smartseo-ai' ); ?></option>
                        <?php foreach ( $post_types as $post_type ) : ?>
                            <option value="<?php echo esc_attr( $post_type->name ); ?>">
                                <?php echo esc_html( $post_type->labels->name ); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    
                    <select id="smartseo-ai-filter-score" class="smartseo-ai-select">
                        <option value=""><?php esc_html_e( 'Tous les scores', 'smartseo-ai' ); ?></option>
                        <option value="good"><?php esc_html_e( 'Bon (80-100)', 'smartseo-ai' ); ?></option>
                        <option value="average"><?php esc_html_e( 'Moyen (50-79)', 'smartseo-ai' ); ?></option>
                        <option value="poor"><?php esc_html_e( 'Faible (0-49)', 'smartseo-ai' ); ?></option>
                    </select>
                </div>
                
                <div class="smartseo-ai-bulk-audit-export">
                    <button type="button" class="button" id="smartseo-ai-export-csv">
                        <span class="dashicons dashicons-media-spreadsheet"></span>
                        <?php esc_html_e( 'Exporter en CSV', 'smartseo-ai' ); ?>
                    </button>
                    
                    <button type="button" class="button" id="smartseo-ai-export-pdf">
                        <span class="dashicons dashicons-pdf"></span>
                        <?php esc_html_e( 'Exporter en PDF', 'smartseo-ai' ); ?>
                    </button>
                </div>
            </div>
            
            <form id="smartseo-ai-bulk-audit-table-form" method="post">
                <?php $audit_table->display(); ?>
            </form>
        </div>
    </div>
</div>

<!-- Modales -->
<div id="smartseo-ai-modal-auto-fix" class="smartseo-ai-modal" style="display: none;">
    <div class="smartseo-ai-modal-content">
        <span class="smartseo-ai-modal-close">&times;</span>
        <h3><?php esc_html_e( 'Correction automatique', 'smartseo-ai' ); ?></h3>
        
        <div class="smartseo-ai-modal-body">
            <p><?php esc_html_e( 'Sélectionnez les problèmes à corriger automatiquement :', 'smartseo-ai' ); ?></p>
            
            <div class="smartseo-ai-checkbox-group">
                <label>
                    <input type="checkbox" name="fix_issues[]" value="meta_description" checked>
                    <?php esc_html_e( 'Meta description manquante ou trop courte', 'smartseo-ai' ); ?>
                </label>
                
                <label>
                    <input type="checkbox" name="fix_issues[]" value="image_alt" checked>
                    <?php esc_html_e( 'Images sans attribut alt', 'smartseo-ai' ); ?>
                </label>
            </div>
            
            <div class="smartseo-ai-modal-actions">
                <button type="button" class="button button-primary" id="smartseo-ai-confirm-auto-fix">
                    <?php esc_html_e( 'Corriger', 'smartseo-ai' ); ?>
                </button>
                
                <button type="button" class="button button-secondary smartseo-ai-modal-cancel">
                    <?php esc_html_e( 'Annuler', 'smartseo-ai' ); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<div id="smartseo-ai-modal-report" class="smartseo-ai-modal" style="display: none;">
    <div class="smartseo-ai-modal-content smartseo-ai-modal-large">
        <span class="smartseo-ai-modal-close">&times;</span>
        <h3><?php esc_html_e( 'Rapport d\'audit SEO', 'smartseo-ai' ); ?></h3>
        
        <div class="smartseo-ai-modal-body">
            <div class="smartseo-ai-modal-loading">
                <div class="smartseo-ai-spinner"></div>
                <p><?php esc_html_e( 'Chargement du rapport...', 'smartseo-ai' ); ?></p>
            </div>
            
            <div class="smartseo-ai-report-content" style="display: none;">
                <!-- Le contenu du rapport sera inséré ici via JavaScript -->
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Variables globales
    var isAuditRunning = false;
    var auditCancelled = false;
    var currentPostId = 0;
    
    // Lancer l'audit en masse
    $('#smartseo-ai-bulk-audit-form').on('submit', function(e) {
        e.preventDefault();
        
        if (isAuditRunning) {
            return;
        }
        
        // Récupérer les paramètres
        var postTypes = [];
        $('input[name="post_types[]"]:checked').each(function() {
            postTypes.push($(this).val());
        });
        
        if (postTypes.length === 0) {
            alert('<?php esc_attr_e( 'Veuillez sélectionner au moins un type de publication.', 'smartseo-ai' ); ?>');
            return;
        }
        
        var limit = $('#limit').val();
        
        // Initialiser l'audit
        isAuditRunning = true;
        auditCancelled = false;
        
        // Afficher la barre de progression
        $('.smartseo-ai-bulk-audit-progress').show();
        $('.smartseo-ai-progress-value').css('width', '0%');
        $('.smartseo-ai-progress-percent').text('0%');
        $('.smartseo-ai-progress-count').text('0 / 0');
        $('.smartseo-ai-progress-status').text('<?php esc_attr_e( 'Préparation de l\'audit...', 'smartseo-ai' ); ?>');
        
        // Lancer l'audit
        runBulkAudit(postTypes, limit, 0);
    });
    
    // Fonction pour exécuter l'audit en masse
    function runBulkAudit(postTypes, limit, offset) {
        if (auditCancelled) {
            isAuditRunning = false;
            $('.smartseo-ai-bulk-audit-progress').hide();
            return;
        }
        
        // Mettre à jour le statut
        $('.smartseo-ai-progress-status').text('<?php esc_attr_e( 'Audit en cours...', 'smartseo-ai' ); ?>');
        
        // Envoyer la requête AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_run_bulk_audit',
                post_types: postTypes,
                limit: 10, // Traiter 10 pages à la fois
                offset: offset,
                nonce: '<?php echo wp_create_nonce( 'smartseo_ai_nonce' ); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Mettre à jour la progression
                    var processed = response.data.offset;
                    var total = response.data.total;
                    var percent = Math.round((processed / total) * 100);
                    
                    $('.smartseo-ai-progress-value').css('width', percent + '%');
                    $('.smartseo-ai-progress-percent').text(percent + '%');
                    $('.smartseo-ai-progress-count').text(processed + ' / ' + total);
                    
                    // Si l'audit n'est pas terminé, continuer
                    if (!response.data.complete && !auditCancelled) {
                        runBulkAudit(postTypes, limit, response.data.offset);
                    } else {
                        // Audit terminé
                        isAuditRunning = false;
                        $('.smartseo-ai-progress-status').text('<?php esc_attr_e( 'Audit terminé !', 'smartseo-ai' ); ?>');
                        
                        // Recharger le tableau des résultats
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    }
                } else {
                    // Erreur
                    isAuditRunning = false;
                    $('.smartseo-ai-progress-status').text('<?php esc_attr_e( 'Erreur : ', 'smartseo-ai' ); ?>' + response.data.message);
                }
            },
            error: function() {
                // Erreur
                isAuditRunning = false;
                $('.smartseo-ai-progress-status').text('<?php esc_attr_e( 'Erreur de communication avec le serveur.', 'smartseo-ai' ); ?>');
            }
        });
    }
    
    // Annuler l'audit
    $('#smartseo-ai-cancel-audit').on('click', function() {
        if (isAuditRunning) {
            auditCancelled = true;
            $('.smartseo-ai-progress-status').text('<?php esc_attr_e( 'Annulation de l\'audit...', 'smartseo-ai' ); ?>');
        }
    });
    
    // Filtrer le tableau des résultats
    $('#smartseo-ai-filter-post-type, #smartseo-ai-filter-score').on('change', function() {
        var postType = $('#smartseo-ai-filter-post-type').val();
        var score = $('#smartseo-ai-filter-score').val();
        
        // Filtrer les lignes du tableau
        $('#smartseo-ai-bulk-audit-table-form tbody tr').each(function() {
            var row = $(this);
            var rowPostType = row.find('td.column-post_type').text().trim();
            var rowScore = parseInt(row.find('.smartseo-ai-score').text().trim());
            var showRow = true;
            
            // Filtrer par type de publication
            if (postType && rowPostType !== postType) {
                showRow = false;
            }
            
            // Filtrer par score
            if (score === 'good' && rowScore < 80) {
                showRow = false;
            } else if (score === 'average' && (rowScore < 50 || rowScore >= 80)) {
                showRow = false;
            } else if (score === 'poor' && rowScore >= 50) {
                showRow = false;
            }
            
            // Afficher ou masquer la ligne
            if (showRow) {
                row.show();
            } else {
                row.hide();
            }
        });
    });
    
    // Exporter en CSV
    $('#smartseo-ai-export-csv').on('click', function() {
        // Récupérer les IDs des lignes sélectionnées
        var ids = [];
        $('input[name="audit_ids[]"]:checked').each(function() {
            ids.push($(this).val());
        });
        
        // Envoyer la requête AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_export_audit',
                format: 'csv',
                ids: ids,
                nonce: '<?php echo wp_create_nonce( 'smartseo_ai_nonce' ); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Créer un lien de téléchargement
                    var blob = new Blob([response.data.export_data], { type: 'text/csv;charset=utf-8;' });
                    var link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = 'smartseo-ai-audit-' + new Date().toISOString().slice(0, 10) + '.csv';
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                    alert('<?php esc_attr_e( 'Erreur lors de l\'exportation.', 'smartseo-ai' ); ?>');
                }
            },
            error: function() {
                alert('<?php esc_attr_e( 'Erreur de communication avec le serveur.', 'smartseo-ai' ); ?>');
            }
        });
    });
    
    // Exporter en PDF
    $('#smartseo-ai-export-pdf').on('click', function() {
        // Récupérer les IDs des lignes sélectionnées
        var ids = [];
        $('input[name="audit_ids[]"]:checked').each(function() {
            ids.push($(this).val());
        });
        
        // Envoyer la requête AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_export_audit',
                format: 'pdf',
                ids: ids,
                nonce: '<?php echo wp_create_nonce( 'smartseo_ai_nonce' ); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Vérifier si c'est une erreur
                    if (response.data.export_data.startsWith('error:')) {
                        alert(response.data.export_data.substring(6));
                        return;
                    }
                    
                    // Créer un lien de téléchargement
                    var binary = atob(response.data.export_data);
                    var array = [];
                    for (var i = 0; i < binary.length; i++) {
                        array.push(binary.charCodeAt(i));
                    }
                    var blob = new Blob([new Uint8Array(array)], { type: 'application/pdf' });
                    var link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = 'smartseo-ai-audit-' + new Date().toISOString().slice(0, 10) + '.pdf';
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                    alert('<?php esc_attr_e( 'Erreur lors de l\'exportation.', 'smartseo-ai' ); ?>');
                }
            },
            error: function() {
                alert('<?php esc_attr_e( 'Erreur de communication avec le serveur.', 'smartseo-ai' ); ?>');
            }
        });
    });
    
    // Réexécuter l'audit
    $(document).on('click', '.smartseo-ai-reaudit', function(e) {
        e.preventDefault();
        
        var postId = $(this).data('post-id');
        var row = $(this).closest('tr');
        
        // Ajouter une classe de chargement à la ligne
        row.addClass('smartseo-ai-loading');
        
        // Envoyer la requête AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_run_bulk_audit',
                post_types: [],
                limit: 1,
                offset: 0,
                post_id: postId,
                nonce: '<?php echo wp_create_nonce( 'smartseo_ai_nonce' ); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Recharger la page
                    location.reload();
                } else {
                    // Erreur
                    row.removeClass('smartseo-ai-loading');
                    alert('<?php esc_attr_e( 'Erreur lors de l\'audit.', 'smartseo-ai' ); ?>');
                }
            },
            error: function() {
                // Erreur
                row.removeClass('smartseo-ai-loading');
                alert('<?php esc_attr_e( 'Erreur de communication avec le serveur.', 'smartseo-ai' ); ?>');
            }
        });
    });
    
    // Ouvrir la modale de correction automatique
    $(document).on('click', '.smartseo-ai-auto-fix', function(e) {
        e.preventDefault();
        
        currentPostId = $(this).data('post-id');
        
        // Ouvrir la modale
        $('#smartseo-ai-modal-auto-fix').show();
    });
    
    // Confirmer la correction automatique
    $('#smartseo-ai-confirm-auto-fix').on('click', function() {
        if (!currentPostId) {
            return;
        }
        
        // Récupérer les problèmes à corriger
        var issues = [];
        $('input[name="fix_issues[]"]:checked').each(function() {
            issues.push($(this).val());
        });
        
        if (issues.length === 0) {
            alert('<?php esc_attr_e( 'Veuillez sélectionner au moins un problème à corriger.', 'smartseo-ai' ); ?>');
            return;
        }
        
        // Fermer la modale
        $('#smartseo-ai-modal-auto-fix').hide();
        
        // Ajouter une classe de chargement à la ligne
        var row = $('tr[data-post-id="' + currentPostId + '"]');
        row.addClass('smartseo-ai-loading');
        
        // Envoyer la requête AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_auto_fix',
                post_id: currentPostId,
                issues: issues,
                nonce: '<?php echo wp_create_nonce( 'smartseo_ai_nonce' ); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Mettre à jour la ligne
                    row.find('.smartseo-ai-score').text(response.data.new_score);
                    row.find('.smartseo-ai-issues').text(response.data.new_issues);
                    
                    // Afficher un message de succès
                    alert('<?php esc_attr_e( 'Corrections appliquées avec succès !', 'smartseo-ai' ); ?>' + '\n\n' + response.data.fixes.join('\n'));
                    
                    // Recharger la page
                    location.reload();
                } else {
                    // Erreur
                    row.removeClass('smartseo-ai-loading');
                    alert('<?php esc_attr_e( 'Erreur lors de la correction.', 'smartseo-ai' ); ?>');
                }
            },
            error: function() {
                // Erreur
                row.removeClass('smartseo-ai-loading');
                alert('<?php esc_attr_e( 'Erreur de communication avec le serveur.', 'smartseo-ai' ); ?>');
            }
        });
    });
    
    // Fermer les modales
    $('.smartseo-ai-modal-close, .smartseo-ai-modal-cancel').on('click', function() {
        $('.smartseo-ai-modal').hide();
    });
    
    // Fermer les modales en cliquant en dehors
    $(window).on('click', function(e) {
        if ($(e.target).hasClass('smartseo-ai-modal')) {
            $('.smartseo-ai-modal').hide();
        }
    });
});
</script>
