/**
 * Styles pour la page d'audit SEO
 */

/* Conteneur principal */
.smartseo-ai-audit-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.smartseo-ai-audit-form {
    flex: 1;
    min-width: 300px;
}

.smartseo-ai-audit-results {
    flex: 2;
    min-width: 500px;
}

/* Cartes */
.smartseo-ai-card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

/* Onglets */
.smartseo-ai-tabs {
    margin-top: 20px;
}

.smartseo-ai-tab-nav {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
}

.smartseo-ai-tab-button {
    background: none;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
    font-weight: 500;
    color: #555;
    border-bottom: 2px solid transparent;
    margin-bottom: -1px;
}

.smartseo-ai-tab-button.active {
    color: #0073aa;
    border-bottom-color: #0073aa;
}

.smartseo-ai-tab-content {
    display: none;
}

.smartseo-ai-tab-content.active {
    display: block;
}

/* Formulaires */
.smartseo-ai-form-group {
    margin-bottom: 15px;
}

.smartseo-ai-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.smartseo-ai-form-actions {
    margin-top: 20px;
}

/* Chargement */
.smartseo-ai-audit-loading {
    text-align: center;
    padding: 30px 0;
}

.smartseo-ai-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 3px solid rgba(0, 115, 170, 0.2);
    border-radius: 50%;
    border-top-color: #0073aa;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Résumé de l'audit */
.smartseo-ai-audit-summary {
    margin-bottom: 30px;
}

.smartseo-ai-score-container {
    display: flex;
    align-items: center;
    gap: 30px;
    margin-top: 20px;
}

.smartseo-ai-score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.smartseo-ai-score-good {
    background: linear-gradient(135deg, #4caf50, #2e7d32);
}

.smartseo-ai-score-average {
    background: linear-gradient(135deg, #ff9800, #e65100);
}

.smartseo-ai-score-poor {
    background: linear-gradient(135deg, #f44336, #b71c1c);
}

.smartseo-ai-score-value {
    font-size: 36px;
    line-height: 1;
}

.smartseo-ai-score-label {
    font-size: 14px;
    opacity: 0.9;
    margin-top: 5px;
}

.smartseo-ai-score-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.smartseo-ai-score-detail {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px 15px;
    background: #f5f5f5;
    border-radius: 4px;
    font-size: 13px;
}

.smartseo-ai-score-detail-value {
    font-weight: bold;
    font-size: 16px;
    margin: 0 5px;
}

.smartseo-ai-score-critical .dashicons {
    color: #f44336;
}

.smartseo-ai-score-warning .dashicons {
    color: #ff9800;
}

.smartseo-ai-score-info .dashicons {
    color: #2196f3;
}

/* Accordéons */
.smartseo-ai-accordion {
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.smartseo-ai-accordion-header {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f9f9f9;
    cursor: pointer;
    position: relative;
}

.smartseo-ai-accordion-header h3 {
    margin: 0;
    flex-grow: 1;
    font-size: 16px;
    margin-left: 10px;
}

.smartseo-ai-accordion-toggle {
    margin-left: 10px;
    transition: transform 0.2s;
}

.smartseo-ai-accordion-header.active .smartseo-ai-accordion-toggle {
    transform: rotate(180deg);
}

.smartseo-ai-accordion-content {
    display: none;
    padding: 20px;
    background: #fff;
    border-top: 1px solid #eee;
}

.smartseo-ai-section-score {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: #fff;
    font-weight: bold;
    margin-left: 10px;
}

/* Statuts */
.smartseo-ai-status-success {
    border-left: 4px solid #4caf50;
}

.smartseo-ai-status-warning {
    border-left: 4px solid #ff9800;
}

.smartseo-ai-status-error {
    border-left: 4px solid #f44336;
}

.smartseo-ai-status-info {
    border-left: 4px solid #2196f3;
}

/* Listes */
.smartseo-ai-issues-list,
.smartseo-ai-recommendations-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.smartseo-ai-issues-list li,
.smartseo-ai-recommendations-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
    padding-left: 25px;
    position: relative;
}

.smartseo-ai-issues-list .dashicons,
.smartseo-ai-recommendations-list .dashicons {
    position: absolute;
    left: 0;
    top: 2px;
}

.smartseo-ai-issues-list .dashicons {
    color: #f44336;
}

.smartseo-ai-recommendations-list .dashicons {
    color: #2196f3;
}

/* Détails */
.smartseo-ai-details-container {
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.smartseo-ai-detail-item {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f5f5f5;
}

.smartseo-ai-detail-label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #555;
}

.smartseo-ai-detail-value {
    background: #f9f9f9;
    padding: 10px;
    border-radius: 4px;
    word-break: break-word;
}

.smartseo-ai-detail-meta {
    font-size: 12px;
    color: #777;
    margin-top: 5px;
}

/* Actions du rapport */
.smartseo-ai-audit-actions {
    margin-top: 30px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.smartseo-ai-audit-actions .dashicons {
    margin-right: 5px;
}

/* Notices */
.smartseo-ai-notice {
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.smartseo-ai-notice-error {
    background-color: #ffebee;
    border-left: 4px solid #f44336;
    color: #b71c1c;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .smartseo-ai-score-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .smartseo-ai-score-circle {
        margin-bottom: 20px;
    }
    
    .smartseo-ai-accordion-header {
        flex-wrap: wrap;
    }
    
    .smartseo-ai-section-score {
        margin-left: auto;
    }
}

/* Impression */
@media print {
    .smartseo-ai-audit-form,
    .smartseo-ai-audit-actions,
    #adminmenumain,
    #wpadminbar,
    .notice,
    .wp-header-end,
    .wrap > h1,
    #wpfooter {
        display: none !important;
    }
    
    .smartseo-ai-audit-results {
        width: 100% !important;
    }
    
    .smartseo-ai-accordion-content {
        display: block !important;
    }
    
    .smartseo-ai-accordion {
        break-inside: avoid;
    }
    
    body {
        background: #fff !important;
    }
    
    .wrap {
        margin: 0 !important;
    }
}
