/**
 * Styles pour la page d'audit SEO en masse
 */

/* Conteneur principal */
.smartseo-ai-bulk-audit-container {
    margin-top: 20px;
}

/* Cartes */
.smartseo-ai-card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

/* Formulaires */
.smartseo-ai-form-group {
    margin-bottom: 15px;
}

.smartseo-ai-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.smartseo-ai-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.smartseo-ai-checkbox-group label {
    margin-right: 15px;
    font-weight: normal;
}

.smartseo-ai-select {
    min-width: 150px;
}

.smartseo-ai-form-actions {
    margin-top: 20px;
}

.smartseo-ai-form-actions .dashicons {
    margin-right: 5px;
    vertical-align: middle;
}

/* Barre de progression */
.smartseo-ai-progress-container {
    margin: 20px 0;
}

.smartseo-ai-progress-bar {
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.smartseo-ai-progress-value {
    height: 100%;
    background-color: #0073aa;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.smartseo-ai-progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
}

.smartseo-ai-progress-status {
    margin: 15px 0;
    font-weight: 500;
}

.smartseo-ai-progress-actions {
    margin-top: 20px;
    text-align: center;
}

/* Tableau des résultats */
.smartseo-ai-bulk-audit-actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.smartseo-ai-bulk-audit-filters {
    display: flex;
    gap: 10px;
}

.smartseo-ai-bulk-audit-export {
    display: flex;
    gap: 10px;
}

.smartseo-ai-bulk-audit-export .dashicons {
    margin-right: 5px;
    vertical-align: middle;
}

/* Scores et problèmes */
.smartseo-ai-score,
.smartseo-ai-issues {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 15px;
    font-weight: bold;
    text-align: center;
    min-width: 40px;
}

.smartseo-ai-score-good {
    background-color: #dff0d8;
    color: #3c763d;
}

.smartseo-ai-score-average {
    background-color: #fcf8e3;
    color: #8a6d3b;
}

.smartseo-ai-score-poor {
    background-color: #f2dede;
    color: #a94442;
}

.smartseo-ai-issues-none {
    background-color: #dff0d8;
    color: #3c763d;
}

.smartseo-ai-issues-few {
    background-color: #fcf8e3;
    color: #8a6d3b;
}

.smartseo-ai-issues-many {
    background-color: #f2dede;
    color: #a94442;
}

/* Ligne en cours de chargement */
.smartseo-ai-loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Modales */
.smartseo-ai-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.smartseo-ai-modal-content {
    background-color: #fefefe;
    margin: 10% auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 80%;
    max-width: 500px;
    position: relative;
}

.smartseo-ai-modal-large {
    max-width: 800px;
}

.smartseo-ai-modal-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.smartseo-ai-modal-close:hover,
.smartseo-ai-modal-close:focus {
    color: black;
    text-decoration: none;
}

.smartseo-ai-modal-body {
    margin-top: 15px;
}

.smartseo-ai-modal-actions {
    margin-top: 20px;
    text-align: right;
}

.smartseo-ai-modal-actions button {
    margin-left: 10px;
}

/* Chargement */
.smartseo-ai-modal-loading {
    text-align: center;
    padding: 30px 0;
}

.smartseo-ai-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 3px solid rgba(0, 115, 170, 0.2);
    border-radius: 50%;
    border-top-color: #0073aa;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Rapport */
.smartseo-ai-report-content {
    max-height: 500px;
    overflow-y: auto;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .smartseo-ai-bulk-audit-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .smartseo-ai-bulk-audit-filters {
        flex-direction: column;
    }
}
