<?php
/**
 * Vue du tableau de bord SEO global
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap">
    <h1><?php _e( 'SmartSEO AI - Dashboard SEO Global', 'smartseo-ai' ); ?></h1>
    
    <div class="smartseo-ai-container smartseo-ai-global-dashboard">
        <div class="smartseo-ai-header">
            <h1><?php _e( 'Dashboard SEO Global', 'smartseo-ai' ); ?></h1>
            <span class="smartseo-ai-version">v<?php echo SMARTSEO_AI_VERSION; ?></span>
        </div>
        
        <div class="smartseo-ai-content">
            <!-- Loader -->
            <div class="smartseo-ai-loader-container">
                <div class="smartseo-ai-loader">
                    <div class="smartseo-ai-loader-spinner"></div>
                    <div class="smartseo-ai-loader-text"><?php _e( 'Chargement des données...', 'smartseo-ai' ); ?></div>
                </div>
            </div>
            
            <!-- Statistiques globales -->
            <div class="smartseo-ai-global-stats">
                <div class="smartseo-ai-stat-card">
                    <h3><?php _e( 'Score SEO moyen', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value" id="average-score">-</div>
                    <div class="stat-description"><?php _e( 'Sur tous les contenus', 'smartseo-ai' ); ?></div>
                </div>
                
                <div class="smartseo-ai-stat-card">
                    <h3><?php _e( 'Contenus optimisés', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value" id="optimized-count">-</div>
                    <div class="stat-description"><?php _e( 'Score ≥ 80', 'smartseo-ai' ); ?></div>
                </div>
                
                <div class="smartseo-ai-stat-card">
                    <h3><?php _e( 'Partiellement optimisés', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value" id="partially-count">-</div>
                    <div class="stat-description"><?php _e( 'Score entre 50 et 79', 'smartseo-ai' ); ?></div>
                </div>
                
                <div class="smartseo-ai-stat-card">
                    <h3><?php _e( 'Non optimisés', 'smartseo-ai' ); ?></h3>
                    <div class="stat-value" id="not-optimized-count">-</div>
                    <div class="stat-description"><?php _e( 'Score < 50', 'smartseo-ai' ); ?></div>
                </div>
            </div>
            
            <!-- Graphiques -->
            <div class="smartseo-ai-charts-container">
                <div class="smartseo-ai-chart-card">
                    <h3><?php _e( 'Évolution du score SEO moyen', 'smartseo-ai' ); ?></h3>
                    <div class="smartseo-ai-chart-wrapper">
                        <canvas id="monthly-scores-chart"></canvas>
                    </div>
                </div>
                
                <div class="smartseo-ai-chart-card">
                    <h3><?php _e( 'Répartition des contenus par niveau d\'optimisation', 'smartseo-ai' ); ?></h3>
                    <div class="smartseo-ai-chart-wrapper">
                        <canvas id="optimization-distribution-chart"></canvas>
                    </div>
                </div>
                
                <div class="smartseo-ai-chart-card">
                    <h3><?php _e( 'Évolution du nombre de contenus optimisés', 'smartseo-ai' ); ?></h3>
                    <div class="smartseo-ai-chart-wrapper">
                        <canvas id="optimization-trend-chart"></canvas>
                    </div>
                </div>
                
                <div class="smartseo-ai-chart-card">
                    <h3><?php _e( 'Erreurs SEO les plus fréquentes', 'smartseo-ai' ); ?></h3>
                    <div class="smartseo-ai-chart-wrapper">
                        <canvas id="error-types-chart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Filtres -->
            <div class="smartseo-ai-filters-container">
                <h2><?php _e( 'Liste détaillée des contenus', 'smartseo-ai' ); ?></h2>
                
                <div class="smartseo-ai-filters">
                    <div class="smartseo-ai-filter-group">
                        <label for="filter-post-type"><?php _e( 'Type de contenu', 'smartseo-ai' ); ?></label>
                        <select id="filter-post-type" class="smartseo-ai-filter">
                            <option value=""><?php _e( 'Tous', 'smartseo-ai' ); ?></option>
                            <?php
                            $post_types = get_post_types( array( 'public' => true ), 'objects' );
                            foreach ( $post_types as $post_type ) {
                                echo '<option value="' . esc_attr( $post_type->name ) . '">' . esc_html( $post_type->labels->singular_name ) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    
                    <div class="smartseo-ai-filter-group">
                        <label for="filter-category"><?php _e( 'Catégorie', 'smartseo-ai' ); ?></label>
                        <select id="filter-category" class="smartseo-ai-filter">
                            <option value="0"><?php _e( 'Toutes', 'smartseo-ai' ); ?></option>
                            <?php
                            $categories = get_categories( array( 'hide_empty' => false ) );
                            foreach ( $categories as $category ) {
                                echo '<option value="' . esc_attr( $category->term_id ) . '">' . esc_html( $category->name ) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    
                    <div class="smartseo-ai-filter-group">
                        <label for="filter-score-range"><?php _e( 'Score SEO', 'smartseo-ai' ); ?></label>
                        <div class="smartseo-ai-range-slider">
                            <div class="range-slider-values">
                                <span id="score-min-value">0</span> - <span id="score-max-value">100</span>
                            </div>
                            <div class="range-slider-container">
                                <input type="range" id="filter-score-min" min="0" max="100" value="0" class="smartseo-ai-filter">
                                <input type="range" id="filter-score-max" min="0" max="100" value="100" class="smartseo-ai-filter">
                            </div>
                        </div>
                    </div>
                    
                    <div class="smartseo-ai-filter-group">
                        <label for="filter-date-start"><?php _e( 'Date', 'smartseo-ai' ); ?></label>
                        <div class="smartseo-ai-date-range">
                            <input type="date" id="filter-date-start" class="smartseo-ai-filter" placeholder="<?php _e( 'Début', 'smartseo-ai' ); ?>">
                            <span>-</span>
                            <input type="date" id="filter-date-end" class="smartseo-ai-filter" placeholder="<?php _e( 'Fin', 'smartseo-ai' ); ?>">
                        </div>
                    </div>
                    
                    <div class="smartseo-ai-filter-group">
                        <label for="filter-status"><?php _e( 'Statut', 'smartseo-ai' ); ?></label>
                        <select id="filter-status" class="smartseo-ai-filter">
                            <option value=""><?php _e( 'Tous', 'smartseo-ai' ); ?></option>
                            <option value="optimized"><?php _e( 'Optimisé', 'smartseo-ai' ); ?></option>
                            <option value="partially"><?php _e( 'Partiellement optimisé', 'smartseo-ai' ); ?></option>
                            <option value="not_optimized"><?php _e( 'Non optimisé', 'smartseo-ai' ); ?></option>
                        </select>
                    </div>
                    
                    <div class="smartseo-ai-filter-actions">
                        <button id="apply-filters" class="button button-primary">
                            <span class="dashicons dashicons-filter"></span>
                            <?php _e( 'Appliquer les filtres', 'smartseo-ai' ); ?>
                        </button>
                        
                        <button id="reset-filters" class="button">
                            <span class="dashicons dashicons-dismiss"></span>
                            <?php _e( 'Réinitialiser', 'smartseo-ai' ); ?>
                        </button>
                    </div>
                </div>
                
                <div class="smartseo-ai-export-container">
                    <button id="export-csv" class="button">
                        <span class="dashicons dashicons-media-spreadsheet"></span>
                        <?php _e( 'Exporter en CSV', 'smartseo-ai' ); ?>
                    </button>
                    
                    <button id="export-excel" class="button">
                        <span class="dashicons dashicons-media-spreadsheet"></span>
                        <?php _e( 'Exporter pour Excel', 'smartseo-ai' ); ?>
                    </button>
                </div>
            </div>
            
            <!-- Tableau des contenus -->
            <div class="smartseo-ai-table-container">
                <table class="smartseo-ai-posts-table" id="global-dashboard-table">
                    <thead>
                        <tr>
                            <th class="column-title"><?php _e( 'Titre', 'smartseo-ai' ); ?></th>
                            <th class="column-type"><?php _e( 'Type', 'smartseo-ai' ); ?></th>
                            <th class="column-score"><?php _e( 'Score SEO', 'smartseo-ai' ); ?></th>
                            <th class="column-status"><?php _e( 'Statut', 'smartseo-ai' ); ?></th>
                            <th class="column-errors"><?php _e( 'Erreurs/Alertes', 'smartseo-ai' ); ?></th>
                            <th class="column-date"><?php _e( 'Dernière mise à jour', 'smartseo-ai' ); ?></th>
                            <th class="column-actions"><?php _e( 'Actions', 'smartseo-ai' ); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="smartseo-ai-no-data">
                            <td colspan="7"><?php _e( 'Chargement des données...', 'smartseo-ai' ); ?></td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- Pagination -->
                <div class="smartseo-ai-pagination">
                    <div class="smartseo-ai-pagination-info">
                        <span id="pagination-info"><?php _e( 'Affichage de 0 à 0 sur 0 éléments', 'smartseo-ai' ); ?></span>
                    </div>
                    
                    <div class="smartseo-ai-pagination-controls">
                        <button id="pagination-first" class="button" disabled>
                            <span class="dashicons dashicons-controls-skipback"></span>
                        </button>
                        <button id="pagination-prev" class="button" disabled>
                            <span class="dashicons dashicons-controls-back"></span>
                        </button>
                        
                        <span id="pagination-current">Page 1</span>
                        
                        <button id="pagination-next" class="button" disabled>
                            <span class="dashicons dashicons-controls-forward"></span>
                        </button>
                        <button id="pagination-last" class="button" disabled>
                            <span class="dashicons dashicons-controls-skipforward"></span>
                        </button>
                    </div>
                    
                    <div class="smartseo-ai-pagination-per-page">
                        <label for="per-page"><?php _e( 'Par page:', 'smartseo-ai' ); ?></label>
                        <select id="per-page" class="smartseo-ai-filter">
                            <option value="20">20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template pour les lignes du tableau -->
<script type="text/template" id="table-row-template">
    <tr data-id="{{id}}">
        <td class="column-title">
            <a href="{{edit_link}}" target="_blank">{{title}}</a>
        </td>
        <td class="column-type">{{post_type}}</td>
        <td class="column-score">
            <div class="smartseo-ai-score-indicator smartseo-ai-score-{{score_class}}">
                {{seo_score}}/100
            </div>
        </td>
        <td class="column-status">
            <div class="smartseo-ai-status-indicator smartseo-ai-status-{{optimization_status}}">
                {{status_label}}
            </div>
        </td>
        <td class="column-errors">
            {{#if error_count}}
                <button class="button button-small smartseo-ai-show-errors" data-id="{{id}}">
                    <span class="dashicons dashicons-warning"></span>
                    {{error_count}} {{error_label}}
                </button>
            {{else}}
                <span class="smartseo-ai-no-errors">
                    <span class="dashicons dashicons-yes-alt"></span>
                    <?php _e( 'Aucune erreur', 'smartseo-ai' ); ?>
                </span>
            {{/if}}
        </td>
        <td class="column-date">{{last_modified}}</td>
        <td class="column-actions">
            <a href="{{edit_link}}" class="button button-small" target="_blank">
                <span class="dashicons dashicons-edit"></span>
                <?php _e( 'Éditer', 'smartseo-ai' ); ?>
            </a>
            <button class="button button-small smartseo-ai-optimize-button" data-id="{{id}}">
                <span class="dashicons dashicons-superhero"></span>
                <?php _e( 'Optimiser', 'smartseo-ai' ); ?>
            </button>
        </td>
    </tr>
</script>

<!-- Template pour la modal des erreurs -->
<script type="text/template" id="errors-modal-template">
    <div class="smartseo-ai-modal-overlay">
        <div class="smartseo-ai-modal">
            <div class="smartseo-ai-modal-header">
                <h2><?php _e( 'Erreurs SEO détectées', 'smartseo-ai' ); ?> - {{title}}</h2>
                <button class="smartseo-ai-modal-close">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>
            <div class="smartseo-ai-modal-content">
                <ul class="smartseo-ai-errors-list">
                    {{#each errors}}
                        <li>{{this}}</li>
                    {{/each}}
                </ul>
            </div>
            <div class="smartseo-ai-modal-footer">
                <a href="{{edit_link}}" class="button button-primary" target="_blank">
                    <span class="dashicons dashicons-edit"></span>
                    <?php _e( 'Éditer l\'article', 'smartseo-ai' ); ?>
                </a>
                <button class="button smartseo-ai-modal-close">
                    <?php _e( 'Fermer', 'smartseo-ai' ); ?>
                </button>
            </div>
        </div>
    </div>
</script>

<!-- Template pour la notification -->
<script type="text/template" id="notification-template">
    <div class="smartseo-ai-notification smartseo-ai-notification-{{type}}">
        <div class="smartseo-ai-notification-icon">
            <span class="dashicons dashicons-{{icon}}"></span>
        </div>
        <div class="smartseo-ai-notification-content">
            <div class="smartseo-ai-notification-message">{{message}}</div>
        </div>
        <button class="smartseo-ai-notification-close">
            <span class="dashicons dashicons-no-alt"></span>
        </button>
    </div>
</script>
