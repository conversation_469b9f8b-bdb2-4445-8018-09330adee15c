<?php
/**
 * Classe pour les suggestions de lisibilité
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour les suggestions de lisibilité
 */
class SmartSEO_AI_Readability_Suggestions extends SmartSEO_AI_Suggestion_Base {

    /**
     * Génère des suggestions de lisibilité
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @return array Suggestions de lisibilité.
     */
    public function generate( $post_id, $content ) {
        // Si le contenu est vide, récupérer le contenu de l'article
        if ( empty( $content ) ) {
            $content = $this->get_post_content( $post_id );
        }
        
        // Analyser la lisibilité du contenu
        $readability_stats = $this->analyze_readability( $content );
        
        // Limiter la taille du contenu pour l'API
        $content = substr( $content, 0, 5000 );
        
        // Générer le prompt pour l'IA
        $prompt = $this->generate_prompt(
            "Analyse la lisibilité de ce contenu et suggère des améliorations pour le rendre plus facile à lire et à comprendre. " .
            "Identifie les phrases trop longues, les paragraphes trop denses, le jargon technique excessif, et les opportunités d'utiliser des listes à puces, des tableaux, ou d'autres éléments visuels. " .
            "Voici quelques statistiques sur le contenu : " . json_encode( $readability_stats ) . ". " .
            "Réponds au format JSON avec : 'readability_score' (0-100), 'readability_level' (easy, medium, difficult), " .
            "'issues' (tableau d'objets avec 'type', 'description', 'example', 'suggestion'), " .
            "'improvement_suggestions' (tableau d'objets avec 'type', 'description', 'example', 'suggestion').",
            array(
                'content' => $content,
            )
        );
        
        // Appeler l'API IA
        $response = $this->call_ai_api( $prompt );
        
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }
        
        // Formater les suggestions
        $suggestions = array(
            'status' => 'success',
            'readability_stats' => $readability_stats,
            'readability_score' => isset( $response['readability_score'] ) ? $response['readability_score'] : 0,
            'readability_level' => isset( $response['readability_level'] ) ? $response['readability_level'] : '',
            'issues' => isset( $response['issues'] ) ? $response['issues'] : array(),
            'improvement_suggestions' => isset( $response['improvement_suggestions'] ) ? $response['improvement_suggestions'] : array(),
        );
        
        // Ajouter des identifiants uniques pour chaque suggestion
        if ( ! empty( $suggestions['issues'] ) ) {
            foreach ( $suggestions['issues'] as $key => $issue ) {
                $suggestions['issues'][$key]['id'] = 'issue_' . $key;
            }
        }
        
        if ( ! empty( $suggestions['improvement_suggestions'] ) ) {
            foreach ( $suggestions['improvement_suggestions'] as $key => $suggestion ) {
                $suggestions['improvement_suggestions'][$key]['id'] = 'improvement_' . $key;
            }
        }
        
        // Enregistrer les suggestions en meta
        update_post_meta( $post_id, 'smartseo_ai_readability_suggestions', $suggestions );
        
        return $suggestions;
    }

    /**
     * Applique une suggestion de lisibilité
     *
     * @param int    $post_id      ID de l'article.
     * @param string $suggestion_id ID de la suggestion.
     * @return mixed Résultat de l'application de la suggestion.
     */
    public function apply( $post_id, $suggestion_id ) {
        // Les suggestions de lisibilité sont généralement des conseils
        // que l'utilisateur doit appliquer manuellement, car elles nécessitent
        // souvent une réécriture du contenu.
        
        return new WP_Error(
            'manual_implementation_required',
            __( 'Les suggestions de lisibilité nécessitent une implémentation manuelle. Veuillez modifier votre contenu en suivant les recommandations.', 'smartseo-ai' )
        );
    }

    /**
     * Analyse la lisibilité du contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Statistiques de lisibilité.
     */
    private function analyze_readability( $content ) {
        // Nettoyer le contenu
        $clean_content = strip_tags( $content );
        
        // Compter les mots
        $word_count = str_word_count( $clean_content );
        
        // Compter les phrases
        $sentences = preg_split( '/[.!?]+/', $clean_content );
        $sentence_count = count( array_filter( $sentences ) );
        
        // Compter les paragraphes
        $paragraphs = preg_split( '/\n\s*\n/', $clean_content );
        $paragraph_count = count( array_filter( $paragraphs ) );
        
        // Calculer la longueur moyenne des phrases
        $avg_sentence_length = $sentence_count > 0 ? $word_count / $sentence_count : 0;
        
        // Calculer la longueur moyenne des paragraphes
        $avg_paragraph_length = $paragraph_count > 0 ? $word_count / $paragraph_count : 0;
        
        // Calculer le score de lisibilité Flesch-Kincaid (approximatif)
        $flesch_score = 206.835 - (1.015 * $avg_sentence_length) - (84.6 * $this->count_syllables( $clean_content ) / $word_count);
        
        return array(
            'word_count' => $word_count,
            'sentence_count' => $sentence_count,
            'paragraph_count' => $paragraph_count,
            'avg_sentence_length' => round( $avg_sentence_length, 1 ),
            'avg_paragraph_length' => round( $avg_paragraph_length, 1 ),
            'flesch_score' => round( $flesch_score ),
        );
    }

    /**
     * Compte approximativement le nombre de syllabes dans un texte
     *
     * @param string $text Texte à analyser.
     * @return int Nombre approximatif de syllabes.
     */
    private function count_syllables( $text ) {
        // Cette méthode est une approximation très simplifiée
        // Une implémentation plus précise nécessiterait un dictionnaire de syllabes
        
        $text = strtolower( $text );
        $text = preg_replace( '/[^a-z]/', ' ', $text );
        $words = explode( ' ', $text );
        $words = array_filter( $words );
        
        $syllable_count = 0;
        
        foreach ( $words as $word ) {
            // Compter les voyelles
            $vowels = preg_match_all( '/[aeiouy]+/', $word, $matches );
            
            // Ajuster pour les diphtongues et les e muets à la fin
            if ( preg_match( '/[aeiouy]e$/', $word ) ) {
                $vowels--;
            }
            
            // Chaque mot a au moins une syllabe
            $syllable_count += max( 1, $vowels );
        }
        
        return $syllable_count;
    }
}
