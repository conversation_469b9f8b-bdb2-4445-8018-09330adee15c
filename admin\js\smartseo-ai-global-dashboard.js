/**
 * Script JavaScript principal pour le Dashboard SEO Global
 *
 * @package SmartSEO_AI
 */

// Initialiser le namespace global
window.SmartSEOAI = window.SmartSEOAI || {};

// Créer le namespace pour le Dashboard SEO Global
window.SmartSEOAI.GlobalDashboard = {
    // Gestionnaire de données
    DataManager: {
        // Données actuelles
        currentData: null,

        // Filtres actuels
        currentFilters: {
            post_type: '',
            category: 0,
            score_min: 0,
            score_max: 100,
            date_start: '',
            date_end: '',
            status: '',
            page: 1,
            per_page: 20
        }
    },

    // Gestionnaire d'interface utilisateur
    UIManager: {},

    // Gestionnaire de graphiques
    ChartManager: {
        // Instances des graphiques
        charts: {
            monthlyScores: null,
            optimizationDistribution: null,
            optimizationTrend: null,
            errorTypes: null
        }
    }
};

(function($) {
    'use strict';

    /**
     * Dashboard SEO Global
     */
    const GlobalDashboard = {
        /**
         * Initialise le Dashboard SEO Global
         */
        init: function() {
            console.log('GlobalDashboard: Initialisation');

            // Initialiser les modules
            this.initDataManager();
            this.initUIManager();
            this.initChartManager();

            // Charger les données initiales
            window.SmartSEOAI.GlobalDashboard.DataManager.loadData();

            console.log('GlobalDashboard: Initialisation terminée');
        },



        /**
         * Initialise le gestionnaire de données
         */
        initDataManager: function() {
            console.log('GlobalDashboard: Initialisation du gestionnaire de données');

            // Définir les méthodes du gestionnaire de données
            const DataManager = window.SmartSEOAI.GlobalDashboard.DataManager;

            // Données actuelles
            DataManager.currentData = null;

            // Filtres actuels
            DataManager.currentFilters = {
                post_type: '',
                category: 0,
                score_min: 0,
                score_max: 100,
                date_start: '',
                date_end: '',
                status: '',
                page: 1,
                per_page: 20
            };

            // Méthode d'initialisation
            DataManager.init = function() {
                console.log('DataManager: Initialisation');

                // Charger les données initiales
                this.loadData();
            };

            // Méthode de chargement des données
            DataManager.loadData = function(callback) {
                console.log('DataManager: Chargement des données');

                // Afficher le loader
                $('.smartseo-ai-loader-container').show();

                // Appeler l'API
                $.ajax({
                    url: smartseoAiGlobalDashboard.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'smartseo_ai_get_global_dashboard_data',
                        nonce: smartseoAiGlobalDashboard.nonce,
                        post_type: this.currentFilters.post_type,
                        category: this.currentFilters.category,
                        score_min: this.currentFilters.score_min,
                        score_max: this.currentFilters.score_max,
                        date_start: this.currentFilters.date_start,
                        date_end: this.currentFilters.date_end,
                        status: this.currentFilters.status,
                        page: this.currentFilters.page,
                        per_page: this.currentFilters.per_page
                    },
                    success: (response) => {
                        // Masquer le loader
                        $('.smartseo-ai-loader-container').hide();

                        if (response.success) {
                            // Stocker les données
                            this.currentData = response.data;

                            // Appeler le callback si fourni
                            if (typeof callback === 'function') {
                                callback(response.data);
                            }

                            // Déclencher un événement pour informer les autres modules
                            $(document).trigger('smartseo-ai-data-loaded', [response.data]);
                        } else {
                            console.error('DataManager: Erreur lors du chargement des données', response);

                            // Afficher une notification d'erreur
                            $(document).trigger('smartseo-ai-show-notification', [{
                                type: 'error',
                                message: response.data.message || smartseoAiGlobalDashboard.i18n.error,
                                icon: 'warning'
                            }]);
                        }
                    },
                    error: (xhr, status, error) => {
                        // Masquer le loader
                        $('.smartseo-ai-loader-container').hide();

                        console.error('DataManager: Erreur AJAX', xhr, status, error);

                        // Afficher une notification d'erreur
                        $(document).trigger('smartseo-ai-show-notification', [{
                            type: 'error',
                            message: smartseoAiGlobalDashboard.i18n.error,
                            icon: 'warning'
                        }]);
                    }
                });
            };

            // Méthode de mise à jour des filtres
            DataManager.updateFilters = function(filters, callback) {
                console.log('DataManager: Mise à jour des filtres', filters);

                // Mettre à jour les filtres
                this.currentFilters = { ...this.currentFilters, ...filters };

                // Recharger les données
                this.loadData(callback);
            };

            // Méthode de réinitialisation des filtres
            DataManager.resetFilters = function(callback) {
                console.log('DataManager: Réinitialisation des filtres');

                // Réinitialiser les filtres
                this.currentFilters = {
                    post_type: '',
                    category: 0,
                    score_min: 0,
                    score_max: 100,
                    date_start: '',
                    date_end: '',
                    status: '',
                    page: 1,
                    per_page: 20
                };

                // Recharger les données
                this.loadData(callback);
            };
        },

        /**
         * Initialise le gestionnaire d'interface utilisateur
         */
        initUIManager: function() {
            console.log('GlobalDashboard: Initialisation du gestionnaire d\'interface utilisateur');

            // Définir les méthodes du gestionnaire d'interface utilisateur
            const UIManager = window.SmartSEOAI.GlobalDashboard.UIManager;

            // Méthode d'initialisation
            UIManager.init = function() {
                console.log('UIManager: Initialisation');

                // Initialiser les composants de l'interface
                this.initNotifications();
                this.initModals();
                this.initFilters();
                this.initPagination();
                this.initExport();
                this.initTableActions();

                // Écouter les événements de données
                this.bindDataEvents();
            };

            // Méthode d'initialisation des notifications
            UIManager.initNotifications = function() {
                console.log('UIManager: Initialisation des notifications');

                // Créer le conteneur de notifications s'il n'existe pas
                if ($('.smartseo-ai-notifications-container').length === 0) {
                    $('body').append('<div class="smartseo-ai-notifications-container"></div>');
                }

                // Écouter l'événement de notification
                $(document).on('smartseo-ai-show-notification', (event, data) => {
                    this.showNotification(data.type, data.message, data.icon);
                });
            };

            // Méthode d'affichage des notifications
            UIManager.showNotification = function(type, message, icon) {
                console.log('UIManager: Affichage d\'une notification', type, message);

                // Récupérer le template
                const template = $('#notification-template').html();

                // Remplacer les variables
                const notification = template
                    .replace('{{type}}', type)
                    .replace('{{message}}', message)
                    .replace('{{icon}}', icon || 'info');

                // Ajouter la notification
                const $notification = $(notification);
                $('.smartseo-ai-notifications-container').append($notification);

                // Animer l'entrée
                setTimeout(() => {
                    $notification.addClass('smartseo-ai-notification-visible');
                }, 10);

                // Ajouter l'écouteur pour fermer la notification
                $notification.find('.smartseo-ai-notification-close').on('click', function() {
                    $(this).closest('.smartseo-ai-notification').removeClass('smartseo-ai-notification-visible');

                    // Supprimer après l'animation
                    setTimeout(() => {
                        $(this).closest('.smartseo-ai-notification').remove();
                    }, 300);
                });

                // Fermer automatiquement après 5 secondes
                setTimeout(() => {
                    $notification.removeClass('smartseo-ai-notification-visible');

                    // Supprimer après l'animation
                    setTimeout(() => {
                        $notification.remove();
                    }, 300);
                }, 5000);
            };

            // Méthode d'initialisation des modales
            UIManager.initModals = function() {
                console.log('UIManager: Initialisation des modales');

                // Écouter les clics sur les boutons d'affichage des erreurs
                $(document).on('click', '.smartseo-ai-show-errors', (event) => {
                    const postId = $(event.currentTarget).data('id');
                    this.showErrorsModal(postId);
                });

                // Écouter les clics sur les boutons de fermeture des modales
                $(document).on('click', '.smartseo-ai-modal-close', () => {
                    this.closeModal();
                });

                // Écouter les clics en dehors des modales
                $(document).on('click', '.smartseo-ai-modal-overlay', (event) => {
                    if ($(event.target).hasClass('smartseo-ai-modal-overlay')) {
                        this.closeModal();
                    }
                });
            };

            // Méthode d'initialisation des filtres
            UIManager.initFilters = function() {
                console.log('UIManager: Initialisation des filtres');

                // Initialiser le slider de score
                this.initScoreSlider();

                // Écouter les clics sur le bouton d'application des filtres
                $('#apply-filters').on('click', () => {
                    this.applyFilters();
                });

                // Écouter les clics sur le bouton de réinitialisation des filtres
                $('#reset-filters').on('click', () => {
                    this.resetFilters();
                });
            };

            // Méthode d'initialisation du slider de score
            UIManager.initScoreSlider = function() {
                console.log('UIManager: Initialisation du slider de score');

                const $minSlider = $('#filter-score-min');
                const $maxSlider = $('#filter-score-max');
                const $minValue = $('#score-min-value');
                const $maxValue = $('#score-max-value');

                // Mettre à jour les valeurs affichées
                $minSlider.on('input', function() {
                    const minVal = parseInt($(this).val());
                    const maxVal = parseInt($maxSlider.val());

                    if (minVal > maxVal) {
                        $maxSlider.val(minVal);
                        $maxValue.text(minVal);
                    }

                    $minValue.text(minVal);
                });

                $maxSlider.on('input', function() {
                    const maxVal = parseInt($(this).val());
                    const minVal = parseInt($minSlider.val());

                    if (maxVal < minVal) {
                        $minSlider.val(maxVal);
                        $minValue.text(maxVal);
                    }

                    $maxValue.text(maxVal);
                });
            };

            // Méthode d'application des filtres
            UIManager.applyFilters = function() {
                console.log('UIManager: Application des filtres');

                // Récupérer les valeurs des filtres
                const filters = {
                    post_type: $('#filter-post-type').val(),
                    category: parseInt($('#filter-category').val()),
                    score_min: parseInt($('#filter-score-min').val()),
                    score_max: parseInt($('#filter-score-max').val()),
                    date_start: $('#filter-date-start').val(),
                    date_end: $('#filter-date-end').val(),
                    status: $('#filter-status').val(),
                    page: 1, // Réinitialiser la pagination
                };

                // Mettre à jour les filtres et recharger les données
                window.SmartSEOAI.GlobalDashboard.DataManager.updateFilters(filters);
            };

            // Méthode de réinitialisation des filtres
            UIManager.resetFilters = function() {
                console.log('UIManager: Réinitialisation des filtres');

                // Réinitialiser les champs de formulaire
                $('#filter-post-type').val('');
                $('#filter-category').val(0);
                $('#filter-score-min').val(0);
                $('#filter-score-max').val(100);
                $('#filter-date-start').val('');
                $('#filter-date-end').val('');
                $('#filter-status').val('');

                // Mettre à jour les valeurs affichées du slider
                $('#score-min-value').text(0);
                $('#score-max-value').text(100);

                // Réinitialiser les filtres et recharger les données
                window.SmartSEOAI.GlobalDashboard.DataManager.resetFilters();
            };

            // Méthode d'initialisation de la pagination
            UIManager.initPagination = function() {
                console.log('UIManager: Initialisation de la pagination');

                // Écouter les clics sur les boutons de pagination
                $('#pagination-first').on('click', () => {
                    this.goToPage(1);
                });

                $('#pagination-prev').on('click', () => {
                    const currentPage = window.SmartSEOAI.GlobalDashboard.DataManager.currentFilters.page;
                    if (currentPage > 1) {
                        this.goToPage(currentPage - 1);
                    }
                });

                $('#pagination-next').on('click', () => {
                    const currentPage = window.SmartSEOAI.GlobalDashboard.DataManager.currentFilters.page;
                    const totalPages = window.SmartSEOAI.GlobalDashboard.DataManager.currentData?.pagination?.total_pages || 1;

                    if (currentPage < totalPages) {
                        this.goToPage(currentPage + 1);
                    }
                });

                $('#pagination-last').on('click', () => {
                    const totalPages = window.SmartSEOAI.GlobalDashboard.DataManager.currentData?.pagination?.total_pages || 1;
                    this.goToPage(totalPages);
                });

                // Écouter les changements du nombre d'éléments par page
                $('#per-page').on('change', () => {
                    const perPage = parseInt($('#per-page').val());

                    // Mettre à jour les filtres et recharger les données
                    window.SmartSEOAI.GlobalDashboard.DataManager.updateFilters({
                        per_page: perPage,
                        page: 1 // Réinitialiser la pagination
                    });
                });
            };

            // Méthode d'initialisation des fonctionnalités d'export
            UIManager.initExport = function() {
                console.log('UIManager: Initialisation des fonctionnalités d\'export');

                // Écouter les clics sur les boutons d'export
                $('#export-csv').on('click', () => {
                    window.SmartSEOAI.GlobalDashboard.DataManager.exportData('csv');
                });

                $('#export-excel').on('click', () => {
                    window.SmartSEOAI.GlobalDashboard.DataManager.exportData('excel');
                });
            };

            // Méthode d'initialisation des actions du tableau
            UIManager.initTableActions = function() {
                console.log('UIManager: Initialisation des actions du tableau');

                // Écouter les clics sur les boutons d'optimisation
                $(document).on('click', '.smartseo-ai-optimize-button', (event) => {
                    const postId = $(event.currentTarget).data('id');

                    // Demander confirmation
                    if (confirm(smartseoAiGlobalDashboard.i18n.confirm_optimize)) {
                        window.SmartSEOAI.GlobalDashboard.DataManager.optimizePost(postId);
                    }
                });
            };

            // Méthode d'écoute des événements de données
            UIManager.bindDataEvents = function() {
                console.log('UIManager: Écoute des événements de données');

                // Écouter l'événement de chargement des données
                $(document).on('smartseo-ai-data-loaded', (event, data) => {
                    this.updateUI(data);
                });
            };

            // Méthode de mise à jour de l'interface utilisateur
            UIManager.updateUI = function(data) {
                console.log('UIManager: Mise à jour de l\'interface utilisateur');

                // Mettre à jour les statistiques
                this.updateStats(data.stats);

                // Mettre à jour le tableau
                this.updateTable(data.items);

                // Mettre à jour la pagination
                this.updatePagination(data.pagination);
            };
        },

        /**
         * Initialise le gestionnaire de graphiques
         */
        initChartManager: function() {
            console.log('GlobalDashboard: Initialisation du gestionnaire de graphiques');

            // Définir les méthodes du gestionnaire de graphiques
            const ChartManager = window.SmartSEOAI.GlobalDashboard.ChartManager;

            // Instances des graphiques
            ChartManager.charts = {
                monthlyScores: null,
                optimizationDistribution: null,
                optimizationTrend: null,
                errorTypes: null
            };

            // Méthode d'initialisation
            ChartManager.init = function() {
                console.log('ChartManager: Initialisation');

                // Écouter les événements de données
                this.bindDataEvents();
            };

            // Méthode d'écoute des événements de données
            ChartManager.bindDataEvents = function() {
                console.log('ChartManager: Écoute des événements de données');

                // Écouter l'événement de chargement des données
                $(document).on('smartseo-ai-data-loaded', (event, data) => {
                    this.updateCharts(data);
                });
            };

            // Méthode de mise à jour des graphiques
            ChartManager.updateCharts = function(data) {
                console.log('ChartManager: Mise à jour des graphiques');

                // Mettre à jour le graphique des scores mensuels
                this.updateMonthlyScoresChart(data.historical_data.monthly_scores);

                // Mettre à jour le graphique de répartition des contenus
                this.updateOptimizationDistributionChart(data.stats);

                // Mettre à jour le graphique d'évolution des contenus optimisés
                this.updateOptimizationTrendChart(data.historical_data.optimization_trend);

                // Mettre à jour le graphique des types d'erreurs
                this.updateErrorTypesChart(data.stats.error_types);
            };
        }
    };

    // Initialiser le Dashboard SEO Global au chargement du document
    $(document).ready(function() {
        GlobalDashboard.init();
    });

})(jQuery);
