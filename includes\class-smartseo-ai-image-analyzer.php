<?php
/**
 * Classe pour analyser les images
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui analyse les balises alt des images
 */
class SmartSEO_AI_Image_Analyzer {

    /**
     * Constructeur
     */
    public function __construct() {
        // Rien à initialiser pour l'instant
    }

    /**
     * Analyse les images d'un article
     *
     * @param int $post_id ID de l'article à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_post( $post_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return array(
                'status' => 'error',
                'message' => __( 'Article introuvable.', 'smartseo-ai' ),
            );
        }

        // Récupérer le contenu de l'article
        $content = $post->post_content;

        // Analyser les images dans le contenu
        return $this->analyze_content_images( $content );
    }

    /**
     * Analyse les images d'une URL
     *
     * @param string $url URL à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_url( $url ) {
        // Récupérer le contenu de la page
        $response = wp_remote_get( $url );
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }

        $html = wp_remote_retrieve_body( $response );
        if ( empty( $html ) ) {
            return array(
                'status' => 'error',
                'message' => __( 'Impossible de récupérer le contenu de la page.', 'smartseo-ai' ),
            );
        }

        // Créer un objet DOMDocument
        $doc = new DOMDocument();
        @$doc->loadHTML( mb_convert_encoding( $html, 'HTML-ENTITIES', 'UTF-8' ) );
        $xpath = new DOMXPath( $doc );

        // Récupérer toutes les images
        $images = $xpath->query( '//img' );
        $image_data = array();
        $missing_alt = 0;
        $empty_alt = 0;
        $total_images = $images->length;

        foreach ( $images as $img ) {
            $src = $img->getAttribute( 'src' );
            $alt = $img->getAttribute( 'alt' );
            $has_alt = $img->hasAttribute( 'alt' );

            $image_data[] = array(
                'src' => $src,
                'alt' => $alt,
                'has_alt' => $has_alt,
            );

            if ( ! $has_alt ) {
                $missing_alt++;
            } elseif ( empty( $alt ) ) {
                $empty_alt++;
            }
        }

        // Calculer le score
        $score = 0;
        if ( $total_images > 0 ) {
            $score = 100 - ( ( $missing_alt + $empty_alt ) / $total_images * 100 );
        }

        // Préparer les résultats
        $results = array(
            'status' => 'success',
            'total_images' => $total_images,
            'images_with_alt' => $total_images - $missing_alt - $empty_alt,
            'images_without_alt' => $missing_alt,
            'images_with_empty_alt' => $empty_alt,
            'images' => $image_data,
            'score' => round( $score ),
        );

        // Ajouter des recommandations
        $results['issues'] = array();
        $results['recommendations'] = array();

        if ( $missing_alt > 0 ) {
            $results['issues'][] = sprintf(
                _n(
                    '%d image n\'a pas d\'attribut alt.',
                    '%d images n\'ont pas d\'attribut alt.',
                    $missing_alt,
                    'smartseo-ai'
                ),
                $missing_alt
            );
            $results['recommendations'][] = __( 'Ajoutez des attributs alt descriptifs à toutes les images.', 'smartseo-ai' );
        }

        if ( $empty_alt > 0 ) {
            $results['issues'][] = sprintf(
                _n(
                    '%d image a un attribut alt vide.',
                    '%d images ont un attribut alt vide.',
                    $empty_alt,
                    'smartseo-ai'
                ),
                $empty_alt
            );
            $results['recommendations'][] = __( 'Remplissez les attributs alt vides avec des descriptions pertinentes.', 'smartseo-ai' );
        }

        return $results;
    }

    /**
     * Analyse les images dans le contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Résultats de l'analyse.
     */
    private function analyze_content_images( $content ) {
        // Créer un objet DOMDocument
        $doc = new DOMDocument();
        @$doc->loadHTML( mb_convert_encoding( $content, 'HTML-ENTITIES', 'UTF-8' ) );
        $xpath = new DOMXPath( $doc );

        // Récupérer toutes les images
        $images = $xpath->query( '//img' );
        $image_data = array();
        $missing_alt = 0;
        $empty_alt = 0;
        $total_images = $images->length;

        foreach ( $images as $img ) {
            $src = $img->getAttribute( 'src' );
            $alt = $img->getAttribute( 'alt' );
            $has_alt = $img->hasAttribute( 'alt' );

            $image_data[] = array(
                'src' => $src,
                'alt' => $alt,
                'has_alt' => $has_alt,
            );

            if ( ! $has_alt ) {
                $missing_alt++;
            } elseif ( empty( $alt ) ) {
                $empty_alt++;
            }
        }

        // Calculer le score
        $score = 0;
        if ( $total_images > 0 ) {
            $score = 100 - ( ( $missing_alt + $empty_alt ) / $total_images * 100 );
        }

        // Préparer les résultats
        $results = array(
            'status' => 'success',
            'total_images' => $total_images,
            'images_with_alt' => $total_images - $missing_alt - $empty_alt,
            'images_without_alt' => $missing_alt,
            'images_with_empty_alt' => $empty_alt,
            'images' => $image_data,
            'score' => round( $score ),
        );

        // Ajouter des recommandations
        $results['issues'] = array();
        $results['recommendations'] = array();

        if ( $missing_alt > 0 ) {
            $results['issues'][] = sprintf(
                _n(
                    '%d image n\'a pas d\'attribut alt.',
                    '%d images n\'ont pas d\'attribut alt.',
                    $missing_alt,
                    'smartseo-ai'
                ),
                $missing_alt
            );
            $results['recommendations'][] = __( 'Ajoutez des attributs alt descriptifs à toutes les images.', 'smartseo-ai' );
        }

        if ( $empty_alt > 0 ) {
            $results['issues'][] = sprintf(
                _n(
                    '%d image a un attribut alt vide.',
                    '%d images ont un attribut alt vide.',
                    $empty_alt,
                    'smartseo-ai'
                ),
                $empty_alt
            );
            $results['recommendations'][] = __( 'Remplissez les attributs alt vides avec des descriptions pertinentes.', 'smartseo-ai' );
        }

        return $results;
    }
}
