/**
 * Script de débogage pour le Dashboard SEO Global
 *
 * @package SmartSEO_AI
 */

(function($) {
    'use strict';

    /**
     * Débogueur pour le Dashboard SEO Global
     */
    const GlobalDashboardDebug = {
        /**
         * Initialise le débogueur
         */
        init: function() {
            console.log('GlobalDashboardDebug: Initialisation');
            
            // Ajouter le bouton de débogage
            this.addDebugButton();
            
            console.log('GlobalDashboardDebug: Initialisation terminée');
        },
        
        /**
         * Ajoute le bouton de débogage
         */
        addDebugButton: function() {
            console.log('GlobalDashboardDebug: Ajout du bouton de débogage');
            
            // Créer le bouton
            const $button = $('<div id="smartseo-ai-global-dashboard-debug-button" style="position: fixed; bottom: 20px; right: 20px; background-color: #dc3232; color: white; padding: 10px; border-radius: 5px; cursor: pointer; z-index: 9999;">Déboguer Dashboard</div>');
            
            // Ajouter le bouton au corps de la page
            $('body').append($button);
            
            // Ajouter l'écouteur d'événement
            $button.on('click', () => {
                this.debugDashboard();
            });
            
            console.log('GlobalDashboardDebug: Bouton de débogage ajouté');
        },
        
        /**
         * Débogue le Dashboard SEO Global
         */
        debugDashboard: function() {
            console.log('GlobalDashboardDebug: Débogage du Dashboard SEO Global');
            
            // Vérifier le namespace
            this.checkNamespace();
            
            // Vérifier les modules
            this.checkModules();
            
            // Vérifier les données
            this.checkData();
            
            // Vérifier les graphiques
            this.checkCharts();
            
            console.log('GlobalDashboardDebug: Débogage terminé');
        },
        
        /**
         * Vérifie le namespace
         */
        checkNamespace: function() {
            console.log('GlobalDashboardDebug: Vérification du namespace');
            
            // Vérifier si le namespace global est disponible
            if (typeof window.SmartSEOAI === 'undefined') {
                console.error('GlobalDashboardDebug: Le namespace global SmartSEOAI n\'est pas disponible');
                return;
            }
            
            console.log('GlobalDashboardDebug: Le namespace global SmartSEOAI est disponible');
            
            // Vérifier si le namespace du Dashboard SEO Global est disponible
            if (typeof window.SmartSEOAI.GlobalDashboard === 'undefined') {
                console.error('GlobalDashboardDebug: Le namespace SmartSEOAI.GlobalDashboard n\'est pas disponible');
                return;
            }
            
            console.log('GlobalDashboardDebug: Le namespace SmartSEOAI.GlobalDashboard est disponible');
        },
        
        /**
         * Vérifie les modules
         */
        checkModules: function() {
            console.log('GlobalDashboardDebug: Vérification des modules');
            
            // Vérifier si le gestionnaire de données est disponible
            if (typeof window.SmartSEOAI.GlobalDashboard.DataManager === 'undefined') {
                console.error('GlobalDashboardDebug: Le module DataManager n\'est pas disponible');
            } else {
                console.log('GlobalDashboardDebug: Le module DataManager est disponible');
                
                // Vérifier les méthodes du gestionnaire de données
                this.checkMethods(window.SmartSEOAI.GlobalDashboard.DataManager, [
                    'init',
                    'loadData',
                    'updateFilters',
                    'resetFilters',
                    'exportData'
                ]);
            }
            
            // Vérifier si le gestionnaire d'interface utilisateur est disponible
            if (typeof window.SmartSEOAI.GlobalDashboard.UIManager === 'undefined') {
                console.error('GlobalDashboardDebug: Le module UIManager n\'est pas disponible');
            } else {
                console.log('GlobalDashboardDebug: Le module UIManager est disponible');
                
                // Vérifier les méthodes du gestionnaire d'interface utilisateur
                this.checkMethods(window.SmartSEOAI.GlobalDashboard.UIManager, [
                    'init',
                    'initNotifications',
                    'showNotification',
                    'initModals',
                    'initFilters',
                    'initPagination',
                    'initExport',
                    'initTableActions',
                    'bindDataEvents',
                    'updateUI'
                ]);
            }
            
            // Vérifier si le gestionnaire de graphiques est disponible
            if (typeof window.SmartSEOAI.GlobalDashboard.ChartManager === 'undefined') {
                console.error('GlobalDashboardDebug: Le module ChartManager n\'est pas disponible');
            } else {
                console.log('GlobalDashboardDebug: Le module ChartManager est disponible');
                
                // Vérifier les méthodes du gestionnaire de graphiques
                this.checkMethods(window.SmartSEOAI.GlobalDashboard.ChartManager, [
                    'init',
                    'bindDataEvents',
                    'updateCharts'
                ]);
            }
        },
        
        /**
         * Vérifie les méthodes d'un module
         * @param {Object} module Module à vérifier
         * @param {Array} methods Méthodes à vérifier
         */
        checkMethods: function(module, methods) {
            methods.forEach(method => {
                if (typeof module[method] === 'undefined') {
                    console.error(`GlobalDashboardDebug: La méthode ${method} n'est pas disponible`);
                } else {
                    console.log(`GlobalDashboardDebug: La méthode ${method} est disponible`);
                }
            });
        },
        
        /**
         * Vérifie les données
         */
        checkData: function() {
            console.log('GlobalDashboardDebug: Vérification des données');
            
            // Vérifier si les données sont disponibles
            if (typeof window.SmartSEOAI.GlobalDashboard.DataManager.currentData === 'undefined') {
                console.error('GlobalDashboardDebug: Les données ne sont pas disponibles');
                return;
            }
            
            const data = window.SmartSEOAI.GlobalDashboard.DataManager.currentData;
            
            if (data === null) {
                console.warn('GlobalDashboardDebug: Les données sont nulles (pas encore chargées)');
                return;
            }
            
            console.log('GlobalDashboardDebug: Les données sont disponibles', data);
            
            // Vérifier les propriétés des données
            const properties = ['items', 'stats', 'pagination', 'historical_data'];
            
            properties.forEach(property => {
                if (typeof data[property] === 'undefined') {
                    console.error(`GlobalDashboardDebug: La propriété ${property} n'est pas disponible dans les données`);
                } else {
                    console.log(`GlobalDashboardDebug: La propriété ${property} est disponible dans les données`);
                }
            });
        },
        
        /**
         * Vérifie les graphiques
         */
        checkCharts: function() {
            console.log('GlobalDashboardDebug: Vérification des graphiques');
            
            // Vérifier si les graphiques sont disponibles
            if (typeof window.SmartSEOAI.GlobalDashboard.ChartManager.charts === 'undefined') {
                console.error('GlobalDashboardDebug: Les graphiques ne sont pas disponibles');
                return;
            }
            
            const charts = window.SmartSEOAI.GlobalDashboard.ChartManager.charts;
            
            console.log('GlobalDashboardDebug: Les graphiques sont disponibles', charts);
            
            // Vérifier les propriétés des graphiques
            const properties = ['monthlyScores', 'optimizationDistribution', 'optimizationTrend', 'errorTypes'];
            
            properties.forEach(property => {
                if (typeof charts[property] === 'undefined') {
                    console.error(`GlobalDashboardDebug: La propriété ${property} n'est pas disponible dans les graphiques`);
                } else {
                    console.log(`GlobalDashboardDebug: La propriété ${property} est disponible dans les graphiques`);
                }
            });
        }
    };
    
    // Initialiser le débogueur au chargement du document
    $(document).ready(function() {
        GlobalDashboardDebug.init();
    });
    
})(jQuery);
