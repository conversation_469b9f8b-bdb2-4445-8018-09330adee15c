<?php
/**
 * Classe pour les suggestions de rich snippets
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour les suggestions de rich snippets
 */
class SmartSEO_AI_Rich_Snippet_Suggestions extends SmartSEO_AI_Suggestion_Base {

    /**
     * Génère des suggestions de rich snippets
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @return array Suggestions de rich snippets.
     */
    public function generate( $post_id, $content ) {
        $title = $this->get_post_title( $post_id );
        
        // Si le contenu est vide, récupérer le contenu de l'article
        if ( empty( $content ) ) {
            $content = $this->get_post_content( $post_id );
        }
        
        // Limiter la taille du contenu pour l'API
        $content = substr( $content, 0, 5000 );
        
        // Générer le prompt pour l'IA
        $prompt = $this->generate_prompt(
            "Analyse ce contenu et identifie les opportunités d'ajouter des données structurées (Schema.org) pour obtenir des rich snippets dans les résultats de recherche. " .
            "Identifie le type de contenu (article, recette, FAQ, how-to, événement, produit, etc.) et suggère les données structurées appropriées. " .
            "Pour chaque suggestion, explique pourquoi elle est pertinente et comment l'implémenter. " .
            "Réponds au format JSON avec : 'content_type' (le type principal de contenu), " .
            "'recommended_schemas' (tableau d'objets avec 'schema_type', 'relevance', 'description', 'implementation_suggestion'), " .
            "'schema_json' (exemple de code JSON-LD pour le schema principal recommandé).",
            array(
                'title' => $title,
                'content' => $content,
            )
        );
        
        // Appeler l'API IA
        $response = $this->call_ai_api( $prompt );
        
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }
        
        // Formater les suggestions
        $suggestions = array(
            'status' => 'success',
            'content_type' => isset( $response['content_type'] ) ? $response['content_type'] : '',
            'recommended_schemas' => isset( $response['recommended_schemas'] ) ? $response['recommended_schemas'] : array(),
            'schema_json' => isset( $response['schema_json'] ) ? $response['schema_json'] : '',
        );
        
        // Ajouter des identifiants uniques pour chaque suggestion
        if ( ! empty( $suggestions['recommended_schemas'] ) ) {
            foreach ( $suggestions['recommended_schemas'] as $key => $schema ) {
                $suggestions['recommended_schemas'][$key]['id'] = 'schema_' . $key;
            }
        }
        
        // Enregistrer les suggestions en meta
        update_post_meta( $post_id, 'smartseo_ai_rich_snippet_suggestions', $suggestions );
        
        return $suggestions;
    }

    /**
     * Applique une suggestion de rich snippet
     *
     * @param int    $post_id      ID de l'article.
     * @param string $suggestion_id ID de la suggestion.
     * @return mixed Résultat de l'application de la suggestion.
     */
    public function apply( $post_id, $suggestion_id ) {
        // Récupérer les suggestions stockées en meta
        $suggestions = get_post_meta( $post_id, 'smartseo_ai_rich_snippet_suggestions', true );
        
        if ( empty( $suggestions ) ) {
            return new WP_Error( 'no_suggestions', __( 'Aucune suggestion de rich snippet disponible.', 'smartseo-ai' ) );
        }
        
        // Si l'ID de suggestion est 'schema_json', appliquer le JSON-LD principal
        if ( $suggestion_id === 'schema_json' && ! empty( $suggestions['schema_json'] ) ) {
            // Enregistrer le schema JSON-LD
            update_post_meta( $post_id, 'smartseo_ai_schema_json', $suggestions['schema_json'] );
            
            // Vérifier si Yoast SEO est actif
            if ( defined( 'WPSEO_VERSION' ) ) {
                // Yoast SEO a sa propre gestion des schemas, nous ne pouvons pas facilement l'intégrer
                return array(
                    'status' => 'warning',
                    'message' => __( 'Schema JSON-LD enregistré, mais Yoast SEO est actif. Vous devrez peut-être configurer les données structurées dans Yoast SEO.', 'smartseo-ai' ),
                );
            }
            
            return array(
                'status' => 'success',
                'message' => __( 'Schema JSON-LD enregistré avec succès. Il sera ajouté à la page.', 'smartseo-ai' ),
            );
        }
        
        // Pour les autres types de suggestions, nous devons les implémenter manuellement
        return new WP_Error(
            'manual_implementation_required',
            __( 'Cette suggestion de rich snippet nécessite une implémentation manuelle. Veuillez suivre les recommandations pour ajouter les données structurées appropriées.', 'smartseo-ai' )
        );
    }

    /**
     * Ajoute le schema JSON-LD au head de la page
     *
     * @param int $post_id ID de l'article.
     */
    public function output_schema_json_ld( $post_id ) {
        $schema_json = get_post_meta( $post_id, 'smartseo_ai_schema_json', true );
        
        if ( ! empty( $schema_json ) ) {
            echo '<script type="application/ld+json">' . $schema_json . '</script>' . "\n";
        }
    }
}
