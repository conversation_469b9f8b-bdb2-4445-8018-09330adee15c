<?php
/**
 * Classe qui gère le tableau de bord SEO global
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère le tableau de bord SEO global
 */
class SmartSEO_AI_Global_Dashboard {

    /**
     * Instance de la classe
     *
     * @var SmartSEO_AI_Global_Dashboard
     */
    private static $instance = null;

    /**
     * Constructeur
     */
    public function __construct() {
        // Ajouter la page d'administration
        add_action( 'admin_menu', array( $this, 'add_global_dashboard_page' ) );

        // Enregistrer les scripts et styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_assets' ) );

        // Ajouter les actions AJAX
        add_action( 'wp_ajax_smartseo_ai_get_global_dashboard_data', array( $this, 'ajax_get_global_dashboard_data' ) );
        add_action( 'wp_ajax_smartseo_ai_export_global_dashboard_data', array( $this, 'ajax_export_global_dashboard_data' ) );
        add_action( 'wp_ajax_smartseo_ai_optimize_post', array( $this, 'ajax_optimize_post' ) );
    }

    /**
     * Récupère l'instance de la classe
     *
     * @return SmartSEO_AI_Global_Dashboard Instance de la classe.
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Ajoute la page du tableau de bord global dans le menu d'administration
     */
    public function add_global_dashboard_page() {
        add_submenu_page(
            'smartseo-ai',
            __( 'Dashboard SEO Global', 'smartseo-ai' ),
            __( 'Dashboard SEO Global', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai-global-dashboard',
            array( $this, 'render_global_dashboard_page' )
        );
    }

    /**
     * Enregistre les scripts et styles pour le tableau de bord global
     *
     * @param string $hook Hook de la page.
     */
    public function enqueue_assets( $hook ) {
        if ( 'smartseo-ai_page_smartseo-ai-global-dashboard' !== $hook ) {
            return;
        }

        // Enregistrer Chart.js
        wp_enqueue_script(
            'chartjs',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            array(),
            '3.9.1',
            true
        );

        // Enregistrer le script de correction SVG
        wp_enqueue_script(
            'smartseo-ai-svg-fix',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-svg-fix.js',
            array( 'jquery' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Enregistrer le script principal du tableau de bord global
        wp_enqueue_script(
            'smartseo-ai-global-dashboard',
            SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-global-dashboard.js',
            array( 'jquery', 'chartjs', 'smartseo-ai-svg-fix' ),
            SMARTSEO_AI_VERSION,
            true
        );

        // Charger le script de débogage en mode développement
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            wp_enqueue_script(
                'smartseo-ai-global-dashboard-debug',
                SMARTSEO_AI_PLUGIN_URL . 'admin/js/smartseo-ai-global-dashboard-debug.js',
                array( 'jquery', 'smartseo-ai-global-dashboard' ),
                SMARTSEO_AI_VERSION,
                true
            );
        }

        // Localiser le script
        wp_localize_script(
            'smartseo-ai-global-dashboard',
            'smartseoAiGlobalDashboard',
            array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce'    => wp_create_nonce( 'smartseo_ai_global_dashboard_nonce' ),
                'i18n'     => array(
                    'loading'           => __( 'Chargement des données...', 'smartseo-ai' ),
                    'error'             => __( 'Une erreur s\'est produite lors du chargement des données.', 'smartseo-ai' ),
                    'no_data'           => __( 'Aucune donnée disponible.', 'smartseo-ai' ),
                    'export_success'    => __( 'Données exportées avec succès.', 'smartseo-ai' ),
                    'export_error'      => __( 'Une erreur s\'est produite lors de l\'exportation des données.', 'smartseo-ai' ),
                    'optimize_success'  => __( 'Article optimisé avec succès.', 'smartseo-ai' ),
                    'optimize_error'    => __( 'Une erreur s\'est produite lors de l\'optimisation de l\'article.', 'smartseo-ai' ),
                    'confirm_optimize'  => __( 'Êtes-vous sûr de vouloir optimiser cet article ?', 'smartseo-ai' ),
                    'optimized'         => __( 'Optimisé', 'smartseo-ai' ),
                    'partially'         => __( 'Partiellement optimisé', 'smartseo-ai' ),
                    'not_optimized'     => __( 'Non optimisé', 'smartseo-ai' ),
                    'excellent'         => __( 'Excellent', 'smartseo-ai' ),
                    'good'              => __( 'Bon', 'smartseo-ai' ),
                    'average'           => __( 'Moyen', 'smartseo-ai' ),
                    'poor'              => __( 'Faible', 'smartseo-ai' ),
                    'all'               => __( 'Tous', 'smartseo-ai' ),
                    'post'              => __( 'Article', 'smartseo-ai' ),
                    'page'              => __( 'Page', 'smartseo-ai' ),
                    'product'           => __( 'Produit', 'smartseo-ai' ),
                    'custom_post_type'  => __( 'Type de contenu personnalisé', 'smartseo-ai' ),
                ),
            )
        );

        // Enregistrer le style du tableau de bord global
        wp_enqueue_style(
            'smartseo-ai-global-dashboard',
            SMARTSEO_AI_PLUGIN_URL . 'admin/css/smartseo-ai-global-dashboard.css',
            array(),
            SMARTSEO_AI_VERSION
        );
    }

    /**
     * Affiche la page du tableau de bord global
     */
    public function render_global_dashboard_page() {
        // Inclure le template
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/global-dashboard.php';
    }

    /**
     * Récupère les données du tableau de bord global via AJAX
     */
    public function ajax_get_global_dashboard_data() {
        // Vérifier le nonce
        check_ajax_referer( 'smartseo_ai_global_dashboard_nonce', 'nonce' );

        // Vérifier les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les paramètres
        $post_type = isset( $_POST['post_type'] ) ? sanitize_text_field( $_POST['post_type'] ) : '';
        $category = isset( $_POST['category'] ) ? intval( $_POST['category'] ) : 0;
        $score_min = isset( $_POST['score_min'] ) ? intval( $_POST['score_min'] ) : 0;
        $score_max = isset( $_POST['score_max'] ) ? intval( $_POST['score_max'] ) : 100;
        $date_start = isset( $_POST['date_start'] ) ? sanitize_text_field( $_POST['date_start'] ) : '';
        $date_end = isset( $_POST['date_end'] ) ? sanitize_text_field( $_POST['date_end'] ) : '';
        $status = isset( $_POST['status'] ) ? sanitize_text_field( $_POST['status'] ) : '';
        $page = isset( $_POST['page'] ) ? intval( $_POST['page'] ) : 1;
        $per_page = isset( $_POST['per_page'] ) ? intval( $_POST['per_page'] ) : 20;

        // Récupérer les données
        $data = $this->get_global_dashboard_data( $post_type, $category, $score_min, $score_max, $date_start, $date_end, $status, $page, $per_page );

        // Envoyer la réponse
        wp_send_json_success( $data );
    }

    /**
     * Exporte les données du tableau de bord global via AJAX
     */
    public function ajax_export_global_dashboard_data() {
        // Vérifier le nonce
        check_ajax_referer( 'smartseo_ai_global_dashboard_nonce', 'nonce' );

        // Vérifier les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer les paramètres
        $post_type = isset( $_POST['post_type'] ) ? sanitize_text_field( $_POST['post_type'] ) : '';
        $category = isset( $_POST['category'] ) ? intval( $_POST['category'] ) : 0;
        $score_min = isset( $_POST['score_min'] ) ? intval( $_POST['score_min'] ) : 0;
        $score_max = isset( $_POST['score_max'] ) ? intval( $_POST['score_max'] ) : 100;
        $date_start = isset( $_POST['date_start'] ) ? sanitize_text_field( $_POST['date_start'] ) : '';
        $date_end = isset( $_POST['date_end'] ) ? sanitize_text_field( $_POST['date_end'] ) : '';
        $status = isset( $_POST['status'] ) ? sanitize_text_field( $_POST['status'] ) : '';
        $format = isset( $_POST['format'] ) ? sanitize_text_field( $_POST['format'] ) : 'csv';

        // Récupérer toutes les données (sans pagination)
        $data = $this->get_global_dashboard_data( $post_type, $category, $score_min, $score_max, $date_start, $date_end, $status, 1, -1 );

        // Générer le fichier d'export
        $export_data = $this->generate_export_data( $data['items'], $format );

        // Envoyer la réponse
        wp_send_json_success( array(
            'data' => $export_data,
            'filename' => 'smartseo-ai-global-dashboard-' . date( 'Y-m-d' ) . '.' . $format,
        ) );
    }

    /**
     * Récupère les données du tableau de bord global
     *
     * @param string $post_type Type de contenu.
     * @param int    $category  ID de la catégorie.
     * @param int    $score_min Score minimum.
     * @param int    $score_max Score maximum.
     * @param string $date_start Date de début.
     * @param string $date_end   Date de fin.
     * @param string $status     Statut d'optimisation.
     * @param int    $page       Numéro de page.
     * @param int    $per_page   Nombre d'éléments par page.
     * @return array Données du tableau de bord global.
     */
    private function get_global_dashboard_data( $post_type = '', $category = 0, $score_min = 0, $score_max = 100, $date_start = '', $date_end = '', $status = '', $page = 1, $per_page = 20 ) {
        // Préparer les arguments de la requête
        $args = array(
            'post_status'    => 'publish',
            'posts_per_page' => $per_page,
            'paged'          => $page,
            'orderby'        => 'date',
            'order'          => 'DESC',
        );

        // Filtrer par type de contenu
        if ( ! empty( $post_type ) ) {
            $args['post_type'] = $post_type;
        } else {
            // Récupérer tous les types de contenu publics
            $post_types = get_post_types( array( 'public' => true ), 'names' );
            $args['post_type'] = array_values( $post_types );
        }

        // Filtrer par catégorie
        if ( $category > 0 ) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'category',
                    'field'    => 'term_id',
                    'terms'    => $category,
                ),
            );
        }

        // Filtrer par date
        if ( ! empty( $date_start ) || ! empty( $date_end ) ) {
            $args['date_query'] = array();

            if ( ! empty( $date_start ) ) {
                $args['date_query']['after'] = $date_start;
            }

            if ( ! empty( $date_end ) ) {
                $args['date_query']['before'] = $date_end;
            }

            $args['date_query']['inclusive'] = true;
        }

        // Filtrer par score SEO
        if ( $score_min > 0 || $score_max < 100 ) {
            $args['meta_query'] = array(
                'relation' => 'AND',
                array(
                    'key'     => 'smartseo_ai_seo_score',
                    'value'   => $score_min,
                    'compare' => '>=',
                    'type'    => 'NUMERIC',
                ),
                array(
                    'key'     => 'smartseo_ai_seo_score',
                    'value'   => $score_max,
                    'compare' => '<=',
                    'type'    => 'NUMERIC',
                ),
            );
        }

        // Filtrer par statut d'optimisation
        if ( ! empty( $status ) ) {
            if ( ! isset( $args['meta_query'] ) ) {
                $args['meta_query'] = array();
            }

            if ( $status === 'optimized' ) {
                $args['meta_query'][] = array(
                    'key'     => 'smartseo_ai_seo_score',
                    'value'   => 80,
                    'compare' => '>=',
                    'type'    => 'NUMERIC',
                );
            } elseif ( $status === 'partially' ) {
                $args['meta_query'][] = array(
                    'key'     => 'smartseo_ai_seo_score',
                    'value'   => array( 50, 79 ),
                    'compare' => 'BETWEEN',
                    'type'    => 'NUMERIC',
                );
            } elseif ( $status === 'not_optimized' ) {
                $args['meta_query'][] = array(
                    'relation' => 'OR',
                    array(
                        'key'     => 'smartseo_ai_seo_score',
                        'value'   => 50,
                        'compare' => '<',
                        'type'    => 'NUMERIC',
                    ),
                    array(
                        'key'     => 'smartseo_ai_seo_score',
                        'compare' => 'NOT EXISTS',
                    ),
                );
            }
        }

        // Exécuter la requête
        $query = new WP_Query( $args );
        $items = array();
        $total_items = $query->found_posts;
        $total_pages = ceil( $total_items / $per_page );

        // Statistiques globales
        $stats = array(
            'total_items'      => $total_items,
            'optimized'        => 0,
            'partially'        => 0,
            'not_optimized'    => 0,
            'average_score'    => 0,
            'total_score'      => 0,
            'score_count'      => 0,
            'error_types'      => array(),
        );

        // Récupérer les données des articles
        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();

                $post_id = get_the_ID();
                $score = get_post_meta( $post_id, 'smartseo_ai_seo_score', true );
                $score = ! empty( $score ) ? intval( $score ) : 0;
                $has_score = ! empty( $score );

                // Déterminer le statut d'optimisation
                $optimization_status = 'not_optimized';
                if ( $has_score ) {
                    if ( $score >= 80 ) {
                        $optimization_status = 'optimized';
                        $stats['optimized']++;
                    } elseif ( $score >= 50 ) {
                        $optimization_status = 'partially';
                        $stats['partially']++;
                    } else {
                        $stats['not_optimized']++;
                    }

                    // Ajouter au score total
                    $stats['total_score'] += $score;
                    $stats['score_count']++;
                } else {
                    $stats['not_optimized']++;
                }

                // Récupérer les erreurs SEO
                $errors = array();
                $seo_advice = get_post_meta( $post_id, 'smartseo_ai_seo_advice', true );
                if ( ! empty( $seo_advice ) ) {
                    // Extraire les erreurs du conseil SEO (format HTML)
                    preg_match_all( '/<li>(.*?)<\/li>/', $seo_advice, $matches );
                    if ( ! empty( $matches[1] ) ) {
                        $errors = $matches[1];

                        // Ajouter aux statistiques des types d'erreurs
                        foreach ( $errors as $error ) {
                            $error_key = sanitize_title( $error );
                            if ( ! isset( $stats['error_types'][$error_key] ) ) {
                                $stats['error_types'][$error_key] = array(
                                    'text'  => $error,
                                    'count' => 0,
                                );
                            }
                            $stats['error_types'][$error_key]['count']++;
                        }
                    }
                }

                // Récupérer la date de dernière mise à jour
                $last_modified = get_post_modified_time( 'Y-m-d H:i:s', true, $post_id );

                // Ajouter l'article à la liste
                $items[] = array(
                    'id'                => $post_id,
                    'title'             => get_the_title(),
                    'permalink'         => get_permalink(),
                    'edit_link'         => get_edit_post_link( $post_id, 'raw' ),
                    'post_type'         => get_post_type_object( get_post_type() )->labels->singular_name,
                    'post_type_slug'    => get_post_type(),
                    'date'              => get_the_date( 'Y-m-d' ),
                    'last_modified'     => $last_modified,
                    'seo_score'         => $score,
                    'has_score'         => $has_score,
                    'optimization_status' => $optimization_status,
                    'errors'            => $errors,
                    'error_count'       => count( $errors ),
                    'categories'        => wp_get_post_categories( $post_id, array( 'fields' => 'names' ) ),
                );
            }

            wp_reset_postdata();
        }

        // Calculer le score moyen
        if ( $stats['score_count'] > 0 ) {
            $stats['average_score'] = round( $stats['total_score'] / $stats['score_count'] );
        }

        // Trier les types d'erreurs par fréquence
        if ( ! empty( $stats['error_types'] ) ) {
            uasort( $stats['error_types'], function( $a, $b ) {
                return $b['count'] - $a['count'];
            } );

            // Limiter à 10 types d'erreurs les plus fréquents
            $stats['error_types'] = array_slice( $stats['error_types'], 0, 10, true );
        }

        // Récupérer les données historiques pour les graphiques
        $historical_data = $this->get_historical_data();

        return array(
            'items'         => $items,
            'stats'         => $stats,
            'pagination'    => array(
                'total_items'  => $total_items,
                'total_pages'  => $total_pages,
                'current_page' => $page,
                'per_page'     => $per_page,
            ),
            'historical_data' => $historical_data,
        );
    }

    /**
     * Récupère les données historiques pour les graphiques
     *
     * @return array Données historiques.
     */
    private function get_historical_data() {
        global $wpdb;

        $data = array(
            'monthly_scores' => array(),
            'optimization_trend' => array(),
        );

        // Récupérer les scores moyens par mois (6 derniers mois)
        $six_months_ago = date( 'Y-m-d', strtotime( '-6 months' ) );

        $monthly_scores_query = $wpdb->prepare(
            "SELECT
                DATE_FORMAT(post_modified, '%%Y-%%m') AS month,
                AVG(meta_value) AS average_score,
                COUNT(*) AS post_count
            FROM
                {$wpdb->posts} p
            JOIN
                {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE
                p.post_status = 'publish'
                AND p.post_modified >= %s
                AND pm.meta_key = 'smartseo_ai_seo_score'
            GROUP BY
                month
            ORDER BY
                month ASC",
            $six_months_ago
        );

        $monthly_scores = $wpdb->get_results( $monthly_scores_query );

        // Formater les données pour le graphique
        $months = array();
        $scores = array();
        $counts = array();

        foreach ( $monthly_scores as $row ) {
            $month_label = date( 'M Y', strtotime( $row->month . '-01' ) );
            $months[] = $month_label;
            $scores[] = round( $row->average_score );
            $counts[] = intval( $row->post_count );
        }

        $data['monthly_scores'] = array(
            'labels' => $months,
            'scores' => $scores,
            'counts' => $counts,
        );

        // Récupérer l'évolution du nombre d'articles optimisés
        $optimization_trend_query = $wpdb->prepare(
            "SELECT
                DATE_FORMAT(post_modified, '%%Y-%%m') AS month,
                SUM(CASE WHEN meta_value >= 80 THEN 1 ELSE 0 END) AS optimized,
                SUM(CASE WHEN meta_value >= 50 AND meta_value < 80 THEN 1 ELSE 0 END) AS partially,
                SUM(CASE WHEN meta_value < 50 THEN 1 ELSE 0 END) AS not_optimized
            FROM
                {$wpdb->posts} p
            JOIN
                {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE
                p.post_status = 'publish'
                AND p.post_modified >= %s
                AND pm.meta_key = 'smartseo_ai_seo_score'
            GROUP BY
                month
            ORDER BY
                month ASC",
            $six_months_ago
        );

        $optimization_trend = $wpdb->get_results( $optimization_trend_query );

        // Formater les données pour le graphique
        $months = array();
        $optimized = array();
        $partially = array();
        $not_optimized = array();

        foreach ( $optimization_trend as $row ) {
            $month_label = date( 'M Y', strtotime( $row->month . '-01' ) );
            $months[] = $month_label;
            $optimized[] = intval( $row->optimized );
            $partially[] = intval( $row->partially );
            $not_optimized[] = intval( $row->not_optimized );
        }

        $data['optimization_trend'] = array(
            'labels' => $months,
            'optimized' => $optimized,
            'partially' => $partially,
            'not_optimized' => $not_optimized,
        );

        return $data;
    }

    /**
     * Optimise un article via AJAX
     */
    public function ajax_optimize_post() {
        // Vérifier le nonce
        check_ajax_referer( 'smartseo_ai_global_dashboard_nonce', 'nonce' );

        // Vérifier les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        // Récupérer l'ID de l'article
        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;

        if ( $post_id <= 0 ) {
            wp_send_json_error( array( 'message' => __( 'ID d\'article invalide.', 'smartseo-ai' ) ) );
        }

        // Vérifier si l'article existe
        $post = get_post( $post_id );

        if ( ! $post ) {
            wp_send_json_error( array( 'message' => __( 'Article introuvable.', 'smartseo-ai' ) ) );
        }

        // Optimiser l'article
        $result = $this->optimize_post( $post_id );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Article optimisé avec succès.', 'smartseo-ai' ),
            'data' => $result
        ) );
    }

    /**
     * Optimise un article
     *
     * @param int $post_id ID de l'article
     * @return array|WP_Error Résultat de l'optimisation
     */
    private function optimize_post( $post_id ) {
        // Vérifier si la classe WP_Error existe
        if ( ! class_exists( 'WP_Error' ) ) {
            require_once ABSPATH . WPINC . '/class-wp-error.php';
        }

        // Vérifier si l'article existe
        $post = get_post( $post_id );

        if ( ! $post ) {
            return new WP_Error( 'invalid_post', __( 'Article introuvable.', 'smartseo-ai' ) );
        }

        // Récupérer le contenu et le titre
        $content = $post->post_content;
        $title = $post->post_title;

        // Déterminer quelle API IA utiliser
        $provider = get_option( 'smartseo_ai_provider', 'openai' );

        if ( $provider === 'openai' ) {
            $ai_api = new SmartSEO_AI_API();
        } else {
            $ai_api = new SmartSEO_AI_Gemini();
        }

        // Analyser le contenu
        $seo_data = $ai_api->analyze_content( $post_id, $content, $title );

        if ( is_wp_error( $seo_data ) ) {
            return $seo_data;
        }

        // Générer une meta description si elle n'existe pas
        if ( empty( get_post_meta( $post_id, 'smartseo_ai_meta_description', true ) ) ) {
            $meta_description = $ai_api->generate_meta_description( $post_id, $content, $title );

            if ( ! is_wp_error( $meta_description ) ) {
                update_post_meta( $post_id, 'smartseo_ai_meta_description', $meta_description );
            }
        }

        // Mettre à jour la date de dernière optimisation
        update_post_meta( $post_id, 'smartseo_ai_last_optimized', current_time( 'mysql' ) );

        return $seo_data;
    }

    /**
     * Génère les données d'export
     *
     * @param array  $items  Éléments à exporter.
     * @param string $format Format d'export (csv ou excel).
     * @return string Données d'export.
     */
    private function generate_export_data( $items, $format = 'csv' ) {
        if ( empty( $items ) ) {
            return '';
        }

        // Définir les en-têtes
        $headers = array(
            __( 'ID', 'smartseo-ai' ),
            __( 'Titre', 'smartseo-ai' ),
            __( 'Type de contenu', 'smartseo-ai' ),
            __( 'Score SEO', 'smartseo-ai' ),
            __( 'Statut d\'optimisation', 'smartseo-ai' ),
            __( 'Nombre d\'erreurs', 'smartseo-ai' ),
            __( 'Date de création', 'smartseo-ai' ),
            __( 'Date de dernière modification', 'smartseo-ai' ),
            __( 'Catégories', 'smartseo-ai' ),
            __( 'URL', 'smartseo-ai' ),
        );

        // Préparer les données
        $data = array();
        $data[] = $headers;

        foreach ( $items as $item ) {
            $status_labels = array(
                'optimized' => __( 'Optimisé', 'smartseo-ai' ),
                'partially' => __( 'Partiellement optimisé', 'smartseo-ai' ),
                'not_optimized' => __( 'Non optimisé', 'smartseo-ai' ),
            );

            $row = array(
                $item['id'],
                $item['title'],
                $item['post_type'],
                $item['has_score'] ? $item['seo_score'] : 0,
                isset( $status_labels[$item['optimization_status']] ) ? $status_labels[$item['optimization_status']] : $item['optimization_status'],
                $item['error_count'],
                $item['date'],
                $item['last_modified'],
                implode( ', ', $item['categories'] ),
                $item['permalink'],
            );

            $data[] = $row;
        }

        // Générer le CSV
        if ( $format === 'csv' ) {
            $output = '';
            foreach ( $data as $row ) {
                $output .= implode( ',', array_map( array( $this, 'csv_escape' ), $row ) ) . "\n";
            }
            return $output;
        }

        // Générer le JSON pour Excel
        if ( $format === 'excel' ) {
            $excel_data = array(
                'headers' => $headers,
                'rows' => array(),
            );

            // Ignorer la première ligne (en-têtes)
            for ( $i = 1; $i < count( $data ); $i++ ) {
                $excel_data['rows'][] = $data[$i];
            }

            return json_encode( $excel_data );
        }

        return '';
    }

    /**
     * Échappe une valeur pour le CSV
     *
     * @param string $value Valeur à échapper.
     * @return string Valeur échappée.
     */
    private function csv_escape( $value ) {
        $value = str_replace( '"', '""', $value );
        return '"' . $value . '"';
    }

    /**
     * Détermine la classe CSS en fonction du score SEO
     *
     * @param int $score Score SEO.
     * @return string Classe CSS.
     */
    public function get_score_class( $score ) {
        if ( $score >= 80 ) {
            return 'excellent';
        } elseif ( $score >= 70 ) {
            return 'good';
        } elseif ( $score >= 50 ) {
            return 'average';
        } else {
            return 'poor';
        }
    }
}
