<?php
/**
 * Classe pour les suggestions de titres et sous-titres
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour les suggestions de titres et sous-titres
 */
class SmartSEO_AI_Heading_Suggestions extends SmartSEO_AI_Suggestion_Base {

    /**
     * Génère des suggestions de titres et sous-titres
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @return array Suggestions de titres et sous-titres.
     */
    public function generate( $post_id, $content ) {
        $title = $this->get_post_title( $post_id );
        
        // Si le contenu est vide, récupérer le contenu de l'article
        if ( empty( $content ) ) {
            $content = $this->get_post_content( $post_id );
        }
        
        // Récupérer le mot-clé principal s'il existe
        $focus_keyword = get_post_meta( $post_id, 'smartseo_ai_focus_keyword', true );
        if ( empty( $focus_keyword ) ) {
            $focus_keyword = get_post_meta( $post_id, '_yoast_wpseo_focuskw', true );
        }
        
        // Extraire les titres existants
        $existing_headings = $this->extract_headings( $content );
        
        // Limiter la taille du contenu pour l'API
        $content = substr( $content, 0, 5000 );
        
        // Générer le prompt pour l'IA
        $prompt = $this->generate_prompt(
            "Analyse ce contenu et suggère des améliorations pour les titres (H1) et sous-titres (H2, H3) pour optimiser le SEO. " .
            ( ! empty( $focus_keyword ) ? "Le mot-clé principal est : $focus_keyword. " : "" ) .
            "Identifie les titres existants qui pourraient être améliorés et suggère de nouveaux sous-titres si nécessaire. " .
            "Réponds au format JSON avec : 'title_suggestion' (suggestion pour le H1 principal), 'existing_headings_improvements' (tableau d'objets avec 'original', 'improved', 'level', 'reason'), et 'new_headings_suggestions' (tableau d'objets avec 'heading', 'level', 'placement', 'reason').",
            array(
                'title' => $title,
                'content' => $content,
            )
        );
        
        // Appeler l'API IA
        $response = $this->call_ai_api( $prompt );
        
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }
        
        // Formater les suggestions
        $suggestions = array(
            'status' => 'success',
            'focus_keyword' => $focus_keyword,
            'title_suggestion' => isset( $response['title_suggestion'] ) ? $response['title_suggestion'] : '',
            'existing_headings' => $existing_headings,
            'existing_headings_improvements' => isset( $response['existing_headings_improvements'] ) ? $response['existing_headings_improvements'] : array(),
            'new_headings_suggestions' => isset( $response['new_headings_suggestions'] ) ? $response['new_headings_suggestions'] : array(),
        );
        
        // Ajouter des identifiants uniques pour chaque suggestion
        if ( ! empty( $suggestions['existing_headings_improvements'] ) ) {
            foreach ( $suggestions['existing_headings_improvements'] as $key => $heading ) {
                $suggestions['existing_headings_improvements'][$key]['id'] = 'existing_' . $key;
            }
        }
        
        if ( ! empty( $suggestions['new_headings_suggestions'] ) ) {
            foreach ( $suggestions['new_headings_suggestions'] as $key => $heading ) {
                $suggestions['new_headings_suggestions'][$key]['id'] = 'new_' . $key;
            }
        }
        
        // Enregistrer les suggestions en meta
        update_post_meta( $post_id, 'smartseo_ai_heading_suggestions', $suggestions );
        
        return $suggestions;
    }

    /**
     * Applique une suggestion de titre ou sous-titre
     *
     * @param int    $post_id      ID de l'article.
     * @param string $suggestion_id ID de la suggestion.
     * @return mixed Résultat de l'application de la suggestion.
     */
    public function apply( $post_id, $suggestion_id ) {
        // Récupérer les suggestions stockées en meta
        $suggestions = get_post_meta( $post_id, 'smartseo_ai_heading_suggestions', true );
        
        if ( empty( $suggestions ) ) {
            return new WP_Error( 'no_suggestions', __( 'Aucune suggestion de titre disponible.', 'smartseo-ai' ) );
        }
        
        $post = get_post( $post_id );
        if ( ! $post ) {
            return new WP_Error( 'invalid_post', __( 'Article introuvable.', 'smartseo-ai' ) );
        }
        
        // Appliquer la suggestion en fonction de son type
        if ( $suggestion_id === 'title' && ! empty( $suggestions['title_suggestion'] ) ) {
            // Mettre à jour le titre de l'article
            wp_update_post( array(
                'ID' => $post_id,
                'post_title' => $suggestions['title_suggestion'],
            ) );
            
            return array(
                'status' => 'success',
                'message' => __( 'Titre principal mis à jour avec succès.', 'smartseo-ai' ),
            );
        } elseif ( strpos( $suggestion_id, 'existing_' ) === 0 && ! empty( $suggestions['existing_headings_improvements'] ) ) {
            // Mettre à jour un titre existant
            $index = substr( $suggestion_id, 9 );
            if ( ! isset( $suggestions['existing_headings_improvements'][$index] ) ) {
                return new WP_Error( 'invalid_suggestion', __( 'Suggestion de titre invalide.', 'smartseo-ai' ) );
            }
            
            $improvement = $suggestions['existing_headings_improvements'][$index];
            $content = $post->post_content;
            
            // Remplacer le titre original par le titre amélioré
            $original_heading = $improvement['original'];
            $improved_heading = $improvement['improved'];
            $level = $improvement['level'];
            
            $content = str_replace(
                "<h{$level}>{$original_heading}</h{$level}>",
                "<h{$level}>{$improved_heading}</h{$level}>",
                $content
            );
            
            // Mettre à jour le contenu de l'article
            wp_update_post( array(
                'ID' => $post_id,
                'post_content' => $content,
            ) );
            
            return array(
                'status' => 'success',
                'message' => __( 'Titre existant amélioré avec succès.', 'smartseo-ai' ),
            );
        } elseif ( strpos( $suggestion_id, 'new_' ) === 0 && ! empty( $suggestions['new_headings_suggestions'] ) ) {
            // Ajouter un nouveau titre
            $index = substr( $suggestion_id, 4 );
            if ( ! isset( $suggestions['new_headings_suggestions'][$index] ) ) {
                return new WP_Error( 'invalid_suggestion', __( 'Suggestion de nouveau titre invalide.', 'smartseo-ai' ) );
            }
            
            // Cette fonctionnalité nécessite une interface utilisateur plus avancée pour permettre à l'utilisateur
            // de choisir où insérer le nouveau titre dans le contenu. Pour l'instant, nous retournons un message
            // indiquant que cette fonctionnalité n'est pas encore disponible.
            
            return new WP_Error(
                'not_implemented',
                __( 'L\'ajout automatique de nouveaux titres n\'est pas encore disponible. Veuillez copier le titre suggéré et l\'insérer manuellement dans votre contenu.', 'smartseo-ai' )
            );
        }
        
        return new WP_Error( 'invalid_suggestion_type', __( 'Type de suggestion de titre invalide.', 'smartseo-ai' ) );
    }

    /**
     * Extrait les titres du contenu
     *
     * @param string $content Contenu à analyser.
     * @return array Titres extraits.
     */
    private function extract_headings( $content ) {
        $headings = array();
        
        // Extraire les balises h1 à h6
        for ( $i = 1; $i <= 6; $i++ ) {
            preg_match_all( "/<h{$i}>(.*?)<\/h{$i}>/i", $content, $matches );
            
            if ( ! empty( $matches[1] ) ) {
                foreach ( $matches[1] as $match ) {
                    $headings[] = array(
                        'text' => $match,
                        'level' => $i,
                    );
                }
            }
        }
        
        return $headings;
    }
}
