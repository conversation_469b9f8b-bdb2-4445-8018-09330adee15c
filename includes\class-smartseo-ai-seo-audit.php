<?php
/**
 * Classe principale pour l'audit SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui gère l'audit SEO complet
 */
class SmartSEO_AI_SEO_Audit {

    /**
     * Instance de la classe
     *
     * @var SmartSEO_AI_SEO_Audit
     */
    private static $instance = null;

    /**
     * Résultats de l'audit
     *
     * @var array
     */
    private $results = array();

    /**
     * Constructeur
     */
    public function __construct() {
        // Ajouter le menu d'administration
        add_action( 'admin_menu', array( $this, 'add_audit_menu' ) );
        
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_smartseo_ai_run_audit', array( $this, 'ajax_run_audit' ) );
    }

    /**
     * Ajoute le sous-menu pour l'audit SEO
     */
    public function add_audit_menu() {
        add_submenu_page(
            'smartseo-ai',
            __( 'Audit SEO', 'smartseo-ai' ),
            __( 'Audit SEO', 'smartseo-ai' ),
            'manage_options',
            'smartseo-ai-audit',
            array( $this, 'render_audit_page' )
        );
    }

    /**
     * Affiche la page d'audit SEO
     */
    public function render_audit_page() {
        // Inclure le template
        include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/seo-audit.php';
    }

    /**
     * Exécute l'audit SEO via AJAX
     */
    public function ajax_run_audit() {
        check_ajax_referer( 'smartseo_ai_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les permissions nécessaires.', 'smartseo-ai' ) ) );
        }

        $post_id = isset( $_POST['post_id'] ) ? intval( $_POST['post_id'] ) : 0;
        $url = isset( $_POST['url'] ) ? esc_url_raw( $_POST['url'] ) : '';

        if ( empty( $post_id ) && empty( $url ) ) {
            wp_send_json_error( array( 'message' => __( 'Veuillez fournir un ID d\'article ou une URL.', 'smartseo-ai' ) ) );
        }

        // Exécuter l'audit
        $results = $this->run_audit( $post_id, $url );

        // Envoyer les résultats
        wp_send_json_success( array( 'results' => $results ) );
    }

    /**
     * Exécute l'audit SEO complet
     *
     * @param int    $post_id ID de l'article à analyser.
     * @param string $url     URL à analyser.
     * @return array Résultats de l'audit.
     */
    public function run_audit( $post_id = 0, $url = '' ) {
        $this->results = array();

        // Initialiser les analyseurs
        $meta_analyzer = new SmartSEO_AI_Meta_Analyzer();
        $image_analyzer = new SmartSEO_AI_Image_Analyzer();
        $heading_analyzer = new SmartSEO_AI_Heading_Analyzer();
        $url_analyzer = new SmartSEO_AI_URL_Analyzer();
        $link_analyzer = new SmartSEO_AI_Link_Analyzer();
        $keyword_analyzer = new SmartSEO_AI_Keyword_Analyzer();
        $robots_sitemap_analyzer = new SmartSEO_AI_Robots_Sitemap_Analyzer();
        $schema_analyzer = new SmartSEO_AI_Schema_Analyzer();

        // Exécuter les analyses
        if ( $post_id > 0 ) {
            // Analyse basée sur l'ID de l'article
            $this->results['meta'] = $meta_analyzer->analyze_post( $post_id );
            $this->results['images'] = $image_analyzer->analyze_post( $post_id );
            $this->results['headings'] = $heading_analyzer->analyze_post( $post_id );
            $this->results['url'] = $url_analyzer->analyze_post( $post_id );
            $this->results['links'] = $link_analyzer->analyze_post( $post_id );
            $this->results['keywords'] = $keyword_analyzer->analyze_post( $post_id );
            $this->results['schema'] = $schema_analyzer->analyze_post( $post_id );
        } else {
            // Analyse basée sur l'URL
            $this->results['meta'] = $meta_analyzer->analyze_url( $url );
            $this->results['images'] = $image_analyzer->analyze_url( $url );
            $this->results['headings'] = $heading_analyzer->analyze_url( $url );
            $this->results['url'] = $url_analyzer->analyze_url( $url );
            $this->results['links'] = $link_analyzer->analyze_url( $url );
            $this->results['keywords'] = $keyword_analyzer->analyze_url( $url );
            $this->results['schema'] = $schema_analyzer->analyze_url( $url );
        }

        // Analyse du robots.txt et sitemap (au niveau du site)
        $this->results['robots_sitemap'] = $robots_sitemap_analyzer->analyze_site();

        // Générer le rapport
        $report = new SmartSEO_AI_Audit_Report( $this->results );
        $this->results['report'] = $report->generate();
        $this->results['score'] = $report->calculate_score();

        return $this->results;
    }

    /**
     * Retourne l'instance unique de la classe
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}
