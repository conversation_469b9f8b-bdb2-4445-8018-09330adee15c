<?php
/**
 * Vue pour la page des suggestions SEO basées sur l'IA
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap smartseo-ai-wrap">
    <h1><?php esc_html_e( 'Suggestions SEO (IA)', 'smartseo-ai' ); ?></h1>
    
    <div class="smartseo-ai-suggestions-container">
        <div class="smartseo-ai-suggestions-form">
            <div class="smartseo-ai-card">
                <h2><?php esc_html_e( 'Obtenir des suggestions SEO intelligentes', 'smartseo-ai' ); ?></h2>
                <p><?php esc_html_e( 'Sélectionnez un article ou une page pour recevoir des suggestions d\'optimisation SEO personnalisées basées sur l\'intelligence artificielle.', 'smartseo-ai' ); ?></p>
                
                <form id="smartseo-ai-suggestions-form">
                    <div class="smartseo-ai-form-group">
                        <label for="post-id"><?php esc_html_e( 'Sélectionnez un article ou une page', 'smartseo-ai' ); ?></label>
                        <select id="post-id" name="post_id" class="smartseo-ai-select2">
                            <option value=""><?php esc_html_e( 'Sélectionnez un article ou une page', 'smartseo-ai' ); ?></option>
                            <?php
                            $posts = get_posts( array(
                                'post_type'      => array( 'post', 'page' ),
                                'post_status'    => 'publish',
                                'posts_per_page' => 100,
                                'orderby'        => 'date',
                                'order'          => 'DESC',
                            ) );

                            foreach ( $posts as $post ) {
                                printf(
                                    '<option value="%d">%s (%s)</option>',
                                    esc_attr( $post->ID ),
                                    esc_html( $post->post_title ),
                                    esc_html( get_post_type_object( $post->post_type )->labels->singular_name )
                                );
                            }
                            ?>
                        </select>
                    </div>
                    
                    <div class="smartseo-ai-form-actions">
                        <button type="submit" class="button button-primary">
                            <span class="dashicons dashicons-superhero"></span>
                            <?php esc_html_e( 'Générer des suggestions', 'smartseo-ai' ); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="smartseo-ai-suggestions-results" style="display: none;">
            <div class="smartseo-ai-card">
                <div class="smartseo-ai-suggestions-loading">
                    <div class="smartseo-ai-spinner"></div>
                    <p><?php esc_html_e( 'Analyse en cours...', 'smartseo-ai' ); ?></p>
                </div>
                
                <div class="smartseo-ai-suggestions-content" style="display: none;">
                    <!-- Le contenu sera inséré ici via JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Templates pour les suggestions -->
<?php include SMARTSEO_AI_PLUGIN_DIR . 'admin/views/partials/suggestions-template.php'; ?>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Initialiser Select2
    $('.smartseo-ai-select2').select2({
        width: '100%',
        placeholder: '<?php esc_attr_e( 'Sélectionnez un article ou une page', 'smartseo-ai' ); ?>',
        allowClear: true
    });
    
    // Soumission du formulaire
    $('#smartseo-ai-suggestions-form').on('submit', function(e) {
        e.preventDefault();
        
        var postId = $('#post-id').val();
        
        if (!postId) {
            alert('<?php esc_attr_e( 'Veuillez sélectionner un article ou une page.', 'smartseo-ai' ); ?>');
            return;
        }
        
        // Afficher la section des résultats
        $('.smartseo-ai-suggestions-results').show();
        $('.smartseo-ai-suggestions-loading').show();
        $('.smartseo-ai-suggestions-content').hide();
        
        // Faire défiler jusqu'aux résultats
        $('html, body').animate({
            scrollTop: $('.smartseo-ai-suggestions-results').offset().top - 50
        }, 500);
        
        // Envoyer la requête AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_get_suggestions',
                post_id: postId,
                nonce: '<?php echo wp_create_nonce( 'smartseo_ai_nonce' ); ?>'
            },
            success: function(response) {
                if (response.success) {
                    renderSuggestions(response.data.suggestions, postId);
                } else {
                    alert(response.data.message || '<?php esc_attr_e( 'Une erreur s\'est produite lors de la génération des suggestions.', 'smartseo-ai' ); ?>');
                    $('.smartseo-ai-suggestions-results').hide();
                }
            },
            error: function() {
                alert('<?php esc_attr_e( 'Une erreur s\'est produite lors de la communication avec le serveur.', 'smartseo-ai' ); ?>');
                $('.smartseo-ai-suggestions-results').hide();
            },
            complete: function() {
                $('.smartseo-ai-suggestions-loading').hide();
            }
        });
    });
    
    // Fonction pour afficher les suggestions
    function renderSuggestions(suggestions, postId) {
        // Utiliser le template Handlebars pour générer le contenu
        var source = $('#suggestions-template').html();
        var template = Handlebars.compile(source);
        var html = template({
            suggestions: suggestions,
            post_id: postId
        });
        
        // Insérer le contenu dans la page
        $('.smartseo-ai-suggestions-content').html(html).show();
        
        // Initialiser les accordéons
        $('.smartseo-ai-accordion-header').on('click', function() {
            $(this).toggleClass('active');
            $(this).next('.smartseo-ai-accordion-content').slideToggle(200);
        });
        
        // Gérer les boutons d'application des suggestions
        $('.smartseo-ai-apply-suggestion').on('click', function() {
            var $button = $(this);
            var suggestionType = $button.data('type');
            var suggestionId = $button.data('id');
            
            $button.prop('disabled', true).addClass('smartseo-ai-loading');
            
            // Envoyer la requête AJAX
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'smartseo_ai_apply_suggestion',
                    post_id: postId,
                    suggestion_type: suggestionType,
                    suggestion_id: suggestionId,
                    nonce: '<?php echo wp_create_nonce( 'smartseo_ai_nonce' ); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        $button.removeClass('smartseo-ai-loading').addClass('smartseo-ai-success');
                        $button.html('<span class="dashicons dashicons-yes"></span> <?php esc_html_e( 'Appliqué', 'smartseo-ai' ); ?>');
                    } else {
                        $button.removeClass('smartseo-ai-loading');
                        alert(response.data.message || '<?php esc_attr_e( 'Une erreur s\'est produite lors de l\'application de la suggestion.', 'smartseo-ai' ); ?>');
                        $button.prop('disabled', false);
                    }
                },
                error: function() {
                    $button.removeClass('smartseo-ai-loading');
                    alert('<?php esc_attr_e( 'Une erreur s\'est produite lors de la communication avec le serveur.', 'smartseo-ai' ); ?>');
                    $button.prop('disabled', false);
                }
            });
        });
    }
    
    // Helpers Handlebars
    Handlebars.registerHelper('if_eq', function(a, b, opts) {
        if (a === b) {
            return opts.fn(this);
        } else {
            return opts.inverse(this);
        }
    });
    
    Handlebars.registerHelper('if_not_eq', function(a, b, opts) {
        if (a !== b) {
            return opts.fn(this);
        } else {
            return opts.inverse(this);
        }
    });
    
    Handlebars.registerHelper('if_gt', function(a, b, opts) {
        if (a > b) {
            return opts.fn(this);
        } else {
            return opts.inverse(this);
        }
    });
    
    Handlebars.registerHelper('if_lt', function(a, b, opts) {
        if (a < b) {
            return opts.fn(this);
        } else {
            return opts.inverse(this);
        }
    });
    
    Handlebars.registerHelper('readability_class', function(score) {
        if (score >= 80) {
            return 'good';
        } else if (score >= 60) {
            return 'average';
        } else {
            return 'poor';
        }
    });
});
</script>
