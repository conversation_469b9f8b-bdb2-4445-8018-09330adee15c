<?php
/**
 * Classe de base pour les suggestions SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe de base pour les suggestions SEO
 */
abstract class SmartSEO_AI_Suggestion_Base {

    /**
     * Instance de l'API IA
     *
     * @var SmartSEO_AI_API|SmartSEO_AI_Gemini
     */
    protected $ai_api;

    /**
     * Constructeur
     *
     * @param SmartSEO_AI_API|SmartSEO_AI_Gemini $ai_api Instance de l'API IA.
     */
    public function __construct( $ai_api ) {
        $this->ai_api = $ai_api;
    }

    /**
     * Génère des suggestions
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @return array Suggestions.
     */
    abstract public function generate( $post_id, $content );

    /**
     * Applique une suggestion
     *
     * @param int    $post_id      ID de l'article.
     * @param string $suggestion_id ID de la suggestion.
     * @return mixed Résultat de l'application de la suggestion.
     */
    abstract public function apply( $post_id, $suggestion_id );

    /**
     * Récupère le contenu d'un article
     *
     * @param int $post_id ID de l'article.
     * @return string Contenu de l'article.
     */
    protected function get_post_content( $post_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return '';
        }
        return $post->post_content;
    }

    /**
     * Récupère le titre d'un article
     *
     * @param int $post_id ID de l'article.
     * @return string Titre de l'article.
     */
    protected function get_post_title( $post_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return '';
        }
        return $post->post_title;
    }

    /**
     * Récupère l'extrait d'un article
     *
     * @param int $post_id ID de l'article.
     * @return string Extrait de l'article.
     */
    protected function get_post_excerpt( $post_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return '';
        }
        return $post->post_excerpt;
    }

    /**
     * Récupère les images d'un article
     *
     * @param int $post_id ID de l'article.
     * @return array Images de l'article.
     */
    protected function get_post_images( $post_id ) {
        $images = array();
        $post = get_post( $post_id );

        if ( ! $post ) {
            return $images;
        }

        // Récupérer l'image mise en avant
        if ( has_post_thumbnail( $post_id ) ) {
            $thumbnail_id = get_post_thumbnail_id( $post_id );
            $thumbnail = get_post( $thumbnail_id );

            if ( $thumbnail ) {
                $images[] = array(
                    'id' => $thumbnail_id,
                    'src' => wp_get_attachment_url( $thumbnail_id ),
                    'alt' => get_post_meta( $thumbnail_id, '_wp_attachment_image_alt', true ),
                    'title' => $thumbnail->post_title,
                    'caption' => $thumbnail->post_excerpt,
                    'description' => $thumbnail->post_content,
                    'is_featured' => true,
                );
            }
        }

        // Récupérer les images dans le contenu
        $content = $post->post_content;
        preg_match_all( '/<img[^>]+>/i', $content, $img_tags );

        foreach ( $img_tags[0] as $img_tag ) {
            preg_match( '/src="([^"]+)"/i', $img_tag, $src );
            preg_match( '/alt="([^"]*)"/i', $img_tag, $alt );
            preg_match( '/class="[^"]*wp-image-([0-9]+)[^"]*"/i', $img_tag, $class );

            if ( ! empty( $src[1] ) ) {
                $image_id = ! empty( $class[1] ) ? intval( $class[1] ) : 0;

                // Vérifier si cette image est déjà dans le tableau (image mise en avant)
                $is_duplicate = false;
                foreach ( $images as $image ) {
                    if ( $image['id'] === $image_id || $image['src'] === $src[1] ) {
                        $is_duplicate = true;
                        break;
                    }
                }

                if ( ! $is_duplicate ) {
                    $images[] = array(
                        'id' => $image_id,
                        'src' => $src[1],
                        'alt' => ! empty( $alt[1] ) ? $alt[1] : '',
                        'is_featured' => false,
                    );
                }
            }
        }

        return $images;
    }

    /**
     * Récupère les articles similaires
     *
     * @param int $post_id ID de l'article.
     * @return array Articles similaires.
     */
    protected function get_similar_posts( $post_id ) {
        $similar_posts = array();
        $post = get_post( $post_id );

        if ( ! $post ) {
            return $similar_posts;
        }

        // Récupérer les catégories de l'article
        $categories = wp_get_post_categories( $post_id );

        if ( empty( $categories ) ) {
            return $similar_posts;
        }

        // Récupérer les articles dans les mêmes catégories
        $args = array(
            'category__in' => $categories,
            'post__not_in' => array( $post_id ),
            'posts_per_page' => 5,
            'post_status' => 'publish',
        );

        $query = new WP_Query( $args );

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();
                $similar_posts[] = array(
                    'id' => get_the_ID(),
                    'title' => get_the_title(),
                    'url' => get_permalink(),
                );
            }
            wp_reset_postdata();
        }

        return $similar_posts;
    }

    /**
     * Génère un prompt pour l'IA
     *
     * @param string $instruction Instruction pour l'IA.
     * @param array  $context     Contexte pour l'IA.
     * @return string Prompt pour l'IA.
     */
    protected function generate_prompt( $instruction, $context = array() ) {
        $prompt = $instruction . "\n\n";

        if ( ! empty( $context['title'] ) ) {
            $prompt .= "Titre: " . $context['title'] . "\n\n";
        }

        if ( ! empty( $context['content'] ) ) {
            $prompt .= "Contenu: " . $context['content'] . "\n\n";
        }

        $prompt .= "Réponds uniquement au format JSON avec les champs spécifiés dans l'instruction.";

        return $prompt;
    }

    /**
     * Appelle l'API IA
     *
     * @param string $prompt Prompt pour l'IA.
     * @return array|WP_Error Réponse de l'IA.
     */
    protected function call_ai_api( $prompt ) {
        // Journaliser le prompt pour le débogage
        error_log( 'SmartSEO AI - Prompt envoyé à l\'API : ' . substr($prompt, 0, 200) . '...' );

        // Appeler l'API IA
        $response = $this->ai_api->generate_content( $prompt );

        // Vérifier si une erreur est retournée
        if ( is_wp_error( $response ) ) {
            error_log( 'SmartSEO AI - Erreur API : ' . $response->get_error_message() );
            return $response;
        }

        // Journaliser la réponse pour le débogage
        error_log( 'SmartSEO AI - Réponse de l\'API : ' . substr($response, 0, 200) . '...' );

        // Essayer de parser la réponse JSON
        $json_response = $this->extract_json_from_response( $response );

        if ( $json_response ) {
            return $json_response;
        }

        // Si la réponse n'est pas un JSON valide, journaliser l'erreur
        error_log( 'SmartSEO AI - Réponse non JSON : ' . $response );

        return new WP_Error( 'invalid_response', __( 'Réponse invalide de l\'IA. La réponse n\'est pas au format JSON attendu.', 'smartseo-ai' ) );
    }

    /**
     * Extrait le JSON de la réponse de l'IA
     *
     * @param string $response Réponse de l'IA.
     * @return array|false Données JSON ou false si invalide.
     */
    protected function extract_json_from_response( $response ) {
        // D'abord, essayer de parser directement la réponse comme JSON
        $data = json_decode( $response, true );
        if ( json_last_error() === JSON_ERROR_NONE ) {
            return $data;
        }

        // Essayer de trouver un objet JSON dans la réponse
        preg_match( '/\{.*\}/s', $response, $matches );

        if ( ! empty( $matches[0] ) ) {
            $json = $matches[0];
            $data = json_decode( $json, true );

            if ( json_last_error() === JSON_ERROR_NONE ) {
                return $data;
            }

            // Journaliser l'erreur JSON
            error_log( 'SmartSEO AI - Erreur JSON : ' . json_last_error_msg() . ' - JSON extrait : ' . $json );
        }

        // Essayer de trouver un tableau JSON dans la réponse
        preg_match( '/\[.*\]/s', $response, $matches );

        if ( ! empty( $matches[0] ) ) {
            $json = $matches[0];
            $data = json_decode( $json, true );

            if ( json_last_error() === JSON_ERROR_NONE ) {
                return $data;
            }
        }

        return false;
    }
}
