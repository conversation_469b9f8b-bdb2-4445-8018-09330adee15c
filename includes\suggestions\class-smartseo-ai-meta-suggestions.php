<?php
/**
 * Classe pour les suggestions de méta descriptions
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour les suggestions de méta descriptions
 */
class SmartSEO_AI_Meta_Suggestions extends SmartSEO_AI_Suggestion_Base {

    /**
     * Génère des suggestions de méta descriptions
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @return array Suggestions de méta descriptions.
     */
    public function generate( $post_id, $content ) {
        $title = $this->get_post_title( $post_id );
        $excerpt = $this->get_post_excerpt( $post_id );
        
        // Si le contenu est vide, récupérer le contenu de l'article
        if ( empty( $content ) ) {
            $content = $this->get_post_content( $post_id );
        }
        
        // Récupérer le mot-clé principal s'il existe
        $focus_keyword = get_post_meta( $post_id, 'smartseo_ai_focus_keyword', true );
        if ( empty( $focus_keyword ) ) {
            $focus_keyword = get_post_meta( $post_id, '_yoast_wpseo_focuskw', true );
        }
        
        // Limiter la taille du contenu pour l'API
        $content = substr( $content, 0, 5000 );
        
        // Générer le prompt pour l'IA
        $prompt = $this->generate_prompt(
            "Génère trois méta descriptions optimisées pour le SEO basées sur ce contenu. Chaque méta description doit faire entre 120 et 155 caractères, inclure le mot-clé principal si possible, et être attrayante pour inciter au clic. " . 
            ( ! empty( $focus_keyword ) ? "Le mot-clé principal est : $focus_keyword. " : "" ) .
            "Réponds au format JSON avec un tableau 'meta_descriptions' contenant des objets avec 'description', 'character_count', et 'includes_keyword' (booléen).",
            array(
                'title' => $title,
                'content' => $content,
                'excerpt' => $excerpt,
            )
        );
        
        // Appeler l'API IA
        $response = $this->call_ai_api( $prompt );
        
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }
        
        // Formater les suggestions
        $suggestions = array(
            'status' => 'success',
            'focus_keyword' => $focus_keyword,
            'meta_descriptions' => isset( $response['meta_descriptions'] ) ? $response['meta_descriptions'] : array(),
        );
        
        // Ajouter des identifiants uniques pour chaque suggestion
        if ( ! empty( $suggestions['meta_descriptions'] ) ) {
            foreach ( $suggestions['meta_descriptions'] as $key => $meta ) {
                $suggestions['meta_descriptions'][$key]['id'] = 'meta_' . $key;
            }
        }
        
        // Enregistrer les suggestions en meta
        update_post_meta( $post_id, 'smartseo_ai_meta_suggestions', $suggestions );
        
        return $suggestions;
    }

    /**
     * Applique une suggestion de méta description
     *
     * @param int    $post_id      ID de l'article.
     * @param string $suggestion_id ID de la suggestion.
     * @return mixed Résultat de l'application de la suggestion.
     */
    public function apply( $post_id, $suggestion_id ) {
        // Récupérer les suggestions stockées en meta
        $suggestions = get_post_meta( $post_id, 'smartseo_ai_meta_suggestions', true );
        
        if ( empty( $suggestions ) || empty( $suggestions['meta_descriptions'] ) ) {
            return new WP_Error( 'no_suggestions', __( 'Aucune suggestion de méta description disponible.', 'smartseo-ai' ) );
        }
        
        // Trouver la suggestion à appliquer
        $meta_description = '';
        
        foreach ( $suggestions['meta_descriptions'] as $meta ) {
            if ( isset( $meta['id'] ) && $meta['id'] === $suggestion_id ) {
                $meta_description = $meta['description'];
                break;
            }
        }
        
        if ( empty( $meta_description ) ) {
            return new WP_Error( 'invalid_suggestion', __( 'Suggestion de méta description invalide.', 'smartseo-ai' ) );
        }
        
        // Enregistrer la méta description
        update_post_meta( $post_id, 'smartseo_ai_meta_description', $meta_description );
        
        // Si Yoast SEO est actif, enregistrer également la méta description dans Yoast
        if ( defined( 'WPSEO_VERSION' ) ) {
            update_post_meta( $post_id, '_yoast_wpseo_metadesc', $meta_description );
        }
        
        // Si All in One SEO est actif, enregistrer également la méta description
        if ( class_exists( 'AIOSEO' ) ) {
            update_post_meta( $post_id, '_aioseo_description', $meta_description );
        }
        
        // Si Rank Math est actif, enregistrer également la méta description
        if ( class_exists( 'RankMath' ) ) {
            update_post_meta( $post_id, 'rank_math_description', $meta_description );
        }
        
        return array(
            'status' => 'success',
            'message' => __( 'Méta description appliquée avec succès.', 'smartseo-ai' ),
        );
    }
}
