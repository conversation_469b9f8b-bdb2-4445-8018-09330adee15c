<?php
/**
 * Classe pour analyser la structure des URL
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui analyse la structure des URL
 */
class SmartSEO_AI_URL_Analyzer {

    /**
     * Constructeur
     */
    public function __construct() {
        // Rien à initialiser pour l'instant
    }

    /**
     * Analyse l'URL d'un article
     *
     * @param int $post_id ID de l'article à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_post( $post_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return array(
                'status' => 'error',
                'message' => __( 'Article introuvable.', 'smartseo-ai' ),
            );
        }

        // Récupérer l'URL de l'article
        $url = get_permalink( $post_id );
        $slug = $post->post_name;
        $title = $post->post_title;

        // Analyser l'URL
        return $this->analyze_url_structure( $url, $slug, $title );
    }

    /**
     * Analyse une URL
     *
     * @param string $url URL à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_url( $url ) {
        // Extraire le slug de l'URL
        $path = parse_url( $url, PHP_URL_PATH );
        $slug = basename( $path );

        // Analyser l'URL
        return $this->analyze_url_structure( $url, $slug );
    }

    /**
     * Analyse la structure d'une URL
     *
     * @param string $url   URL complète.
     * @param string $slug  Slug de l'URL.
     * @param string $title Titre de la page (facultatif).
     * @return array Résultats de l'analyse.
     */
    private function analyze_url_structure( $url, $slug, $title = '' ) {
        $results = array(
            'status' => 'success',
            'url' => $url,
            'slug' => $slug,
            'issues' => array(),
            'recommendations' => array(),
            'score' => 0,
        );

        // Vérifier la longueur du slug
        $slug_length = mb_strlen( $slug );
        if ( $slug_length > 75 ) {
            $results['issues'][] = __( 'Le slug est trop long.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Raccourcissez le slug à moins de 75 caractères.', 'smartseo-ai' );
        }

        // Vérifier si le slug contient des caractères spéciaux
        if ( preg_match( '/[^a-z0-9\-]/', $slug ) ) {
            $results['issues'][] = __( 'Le slug contient des caractères spéciaux.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Utilisez uniquement des lettres minuscules, des chiffres et des tirets dans le slug.', 'smartseo-ai' );
        }

        // Vérifier si le slug contient des mots-clés pertinents
        if ( ! empty( $title ) ) {
            $title_words = $this->get_important_words( $title );
            $slug_words = $this->get_important_words( $slug );
            $common_words = array_intersect( $title_words, $slug_words );

            if ( count( $common_words ) === 0 ) {
                $results['issues'][] = __( 'Le slug ne contient aucun mot-clé du titre.', 'smartseo-ai' );
                $results['recommendations'][] = __( 'Incluez des mots-clés pertinents du titre dans le slug.', 'smartseo-ai' );
            }
        }

        // Vérifier si l'URL contient des paramètres de requête
        if ( strpos( $url, '?' ) !== false ) {
            $results['issues'][] = __( 'L\'URL contient des paramètres de requête.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Évitez d\'utiliser des paramètres de requête dans les URL pour un meilleur référencement.', 'smartseo-ai' );
        }

        // Vérifier si l'URL contient des nombres de pagination
        if ( preg_match( '/\/page\/[0-9]+\/?$/', $url ) ) {
            $results['issues'][] = __( 'L\'URL semble être une page de pagination.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Assurez-vous que les pages de pagination sont correctement balisées avec rel="prev" et rel="next".', 'smartseo-ai' );
        }

        // Calculer le score
        $score = 100;

        // Pénalité pour les problèmes détectés
        $score -= count( $results['issues'] ) * 20;

        // Bonus pour un slug de bonne longueur
        if ( $slug_length >= 3 && $slug_length <= 75 ) {
            $score += 10;
        }

        // Bonus pour un slug contenant des mots-clés du titre
        if ( ! empty( $title ) && count( $common_words ) > 0 ) {
            $score += 10;
        }

        $results['score'] = max( 0, min( 100, $score ) );

        return $results;
    }

    /**
     * Extrait les mots importants d'une chaîne
     *
     * @param string $text Texte à analyser.
     * @return array Mots importants.
     */
    private function get_important_words( $text ) {
        // Convertir en minuscules et supprimer les caractères spéciaux
        $text = strtolower( $text );
        $text = preg_replace( '/[^a-z0-9\s\-]/', '', $text );

        // Diviser en mots
        $words = preg_split( '/[\s\-]+/', $text );

        // Filtrer les mots courts et les mots vides
        $stop_words = array( 'le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'de', 'du', 'a', 'au', 'aux', 'en', 'par', 'pour', 'sur', 'the', 'a', 'an', 'and', 'or', 'of', 'to', 'in', 'on', 'by', 'for' );
        $words = array_filter( $words, function( $word ) use ( $stop_words ) {
            return strlen( $word ) > 2 && ! in_array( $word, $stop_words, true );
        } );

        return array_values( $words );
    }
}
