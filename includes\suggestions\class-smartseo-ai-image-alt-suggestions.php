<?php
/**
 * Classe pour les suggestions de balises alt pour les images
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour les suggestions de balises alt pour les images
 */
class SmartSEO_AI_Image_Alt_Suggestions extends SmartSEO_AI_Suggestion_Base {

    /**
     * Génère des suggestions de balises alt pour les images
     *
     * @param int    $post_id ID de l'article.
     * @param string $content Contenu à analyser.
     * @return array Suggestions de balises alt pour les images.
     */
    public function generate( $post_id, $content ) {
        $title = $this->get_post_title( $post_id );
        
        // Si le contenu est vide, récupérer le contenu de l'article
        if ( empty( $content ) ) {
            $content = $this->get_post_content( $post_id );
        }
        
        // Récupérer les images de l'article
        $images = $this->get_post_images( $post_id );
        
        // Récupérer le mot-clé principal s'il existe
        $focus_keyword = get_post_meta( $post_id, 'smartseo_ai_focus_keyword', true );
        if ( empty( $focus_keyword ) ) {
            $focus_keyword = get_post_meta( $post_id, '_yoast_wpseo_focuskw', true );
        }
        
        // Si aucune image n'a été trouvée, retourner un message d'erreur
        if ( empty( $images ) ) {
            return array(
                'status' => 'error',
                'message' => __( 'Aucune image n\'a été trouvée dans cet article.', 'smartseo-ai' ),
            );
        }
        
        // Identifier les images sans attribut alt ou avec un attribut alt vide
        $images_without_alt = array();
        foreach ( $images as $image ) {
            if ( empty( $image['alt'] ) ) {
                $images_without_alt[] = $image;
            }
        }
        
        // Si toutes les images ont déjà un attribut alt, retourner un message de succès
        if ( empty( $images_without_alt ) ) {
            return array(
                'status' => 'success',
                'message' => __( 'Toutes les images ont déjà un attribut alt.', 'smartseo-ai' ),
                'images' => $images,
                'alt_suggestions' => array(),
            );
        }
        
        // Limiter la taille du contenu pour l'API
        $content = substr( $content, 0, 5000 );
        
        // Générer le prompt pour l'IA
        $prompt = $this->generate_prompt(
            "Analyse ce contenu et suggère des attributs alt descriptifs et optimisés pour le SEO pour les images suivantes : " .
            json_encode( $images_without_alt ) . ". " .
            ( ! empty( $focus_keyword ) ? "Le mot-clé principal est : $focus_keyword. " : "" ) .
            "Pour chaque image, génère un attribut alt qui décrit précisément l'image, inclut le mot-clé principal si pertinent, et est concis (8-10 mots maximum). " .
            "Réponds au format JSON avec un tableau 'alt_suggestions' contenant des objets avec 'image_id', 'image_src', 'suggested_alt', 'reason'.",
            array(
                'title' => $title,
                'content' => $content,
            )
        );
        
        // Appeler l'API IA
        $response = $this->call_ai_api( $prompt );
        
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }
        
        // Formater les suggestions
        $suggestions = array(
            'status' => 'success',
            'focus_keyword' => $focus_keyword,
            'images' => $images,
            'images_without_alt' => $images_without_alt,
            'alt_suggestions' => isset( $response['alt_suggestions'] ) ? $response['alt_suggestions'] : array(),
        );
        
        // Ajouter des identifiants uniques pour chaque suggestion
        if ( ! empty( $suggestions['alt_suggestions'] ) ) {
            foreach ( $suggestions['alt_suggestions'] as $key => $suggestion ) {
                $suggestions['alt_suggestions'][$key]['id'] = 'alt_' . $key;
            }
        }
        
        // Enregistrer les suggestions en meta
        update_post_meta( $post_id, 'smartseo_ai_image_alt_suggestions', $suggestions );
        
        return $suggestions;
    }

    /**
     * Applique une suggestion de balise alt pour une image
     *
     * @param int    $post_id      ID de l'article.
     * @param string $suggestion_id ID de la suggestion.
     * @return mixed Résultat de l'application de la suggestion.
     */
    public function apply( $post_id, $suggestion_id ) {
        // Récupérer les suggestions stockées en meta
        $suggestions = get_post_meta( $post_id, 'smartseo_ai_image_alt_suggestions', true );
        
        if ( empty( $suggestions ) || empty( $suggestions['alt_suggestions'] ) ) {
            return new WP_Error( 'no_suggestions', __( 'Aucune suggestion de balise alt disponible.', 'smartseo-ai' ) );
        }
        
        // Trouver la suggestion à appliquer
        $alt_suggestion = null;
        foreach ( $suggestions['alt_suggestions'] as $suggestion ) {
            if ( isset( $suggestion['id'] ) && $suggestion['id'] === $suggestion_id ) {
                $alt_suggestion = $suggestion;
                break;
            }
        }
        
        if ( ! $alt_suggestion ) {
            return new WP_Error( 'invalid_suggestion', __( 'Suggestion de balise alt invalide.', 'smartseo-ai' ) );
        }
        
        $image_id = $alt_suggestion['image_id'];
        $suggested_alt = $alt_suggestion['suggested_alt'];
        
        // Si l'image a un ID (image de la médiathèque), mettre à jour l'attribut alt
        if ( $image_id > 0 ) {
            update_post_meta( $image_id, '_wp_attachment_image_alt', $suggested_alt );
            
            return array(
                'status' => 'success',
                'message' => __( 'Attribut alt mis à jour avec succès.', 'smartseo-ai' ),
            );
        } else {
            // Si l'image n'a pas d'ID (image externe ou non enregistrée dans la médiathèque),
            // nous devons mettre à jour le contenu de l'article pour ajouter l'attribut alt
            $post = get_post( $post_id );
            if ( ! $post ) {
                return new WP_Error( 'invalid_post', __( 'Article introuvable.', 'smartseo-ai' ) );
            }
            
            $content = $post->post_content;
            $image_src = $alt_suggestion['image_src'];
            
            // Trouver la balise img correspondante
            preg_match_all( '/<img[^>]+src=([\'"])' . preg_quote( $image_src, '/' ) . '\1[^>]*>/i', $content, $matches );
            
            if ( empty( $matches[0] ) ) {
                return new WP_Error(
                    'image_not_found',
                    sprintf(
                        __( 'L\'image avec la source "%s" n\'a pas été trouvée dans le contenu.', 'smartseo-ai' ),
                        $image_src
                    )
                );
            }
            
            $img_tag = $matches[0][0];
            
            // Vérifier si la balise img a déjà un attribut alt
            if ( preg_match( '/alt=([\'"])(.*?)\1/i', $img_tag ) ) {
                // Remplacer l'attribut alt existant
                $new_img_tag = preg_replace(
                    '/alt=([\'"])(.*?)\1/i',
                    'alt="' . esc_attr( $suggested_alt ) . '"',
                    $img_tag
                );
            } else {
                // Ajouter un attribut alt
                $new_img_tag = str_replace( '>', ' alt="' . esc_attr( $suggested_alt ) . '">', $img_tag );
            }
            
            // Mettre à jour le contenu de l'article
            $new_content = str_replace( $img_tag, $new_img_tag, $content );
            wp_update_post( array(
                'ID' => $post_id,
                'post_content' => $new_content,
            ) );
            
            return array(
                'status' => 'success',
                'message' => __( 'Attribut alt ajouté avec succès.', 'smartseo-ai' ),
            );
        }
    }
}
