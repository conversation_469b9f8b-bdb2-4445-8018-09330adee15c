<?php
/**
 * Classe pour analyser la densité des mots-clés
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui analyse la densité et l'usage des mots-clés
 */
class SmartSEO_AI_Keyword_Analyzer {

    /**
     * Constructeur
     */
    public function __construct() {
        // Rien à initialiser pour l'instant
    }

    /**
     * Analyse les mots-clés d'un article
     *
     * @param int $post_id ID de l'article à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_post( $post_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return array(
                'status' => 'error',
                'message' => __( 'Article introuvable.', 'smartseo-ai' ),
            );
        }

        // Récupérer le contenu de l'article
        $content = $post->post_content;
        $title = $post->post_title;
        $excerpt = $post->post_excerpt;

        // Récupérer les mots-clés définis
        $focus_keyword = get_post_meta( $post_id, '_yoast_wpseo_focuskw', true );
        if ( empty( $focus_keyword ) ) {
            $focus_keyword = get_post_meta( $post_id, 'smartseo_ai_focus_keyword', true );
        }

        // Si aucun mot-clé n'est défini, on essaie de le déduire du titre
        if ( empty( $focus_keyword ) ) {
            $focus_keyword = $this->extract_focus_keyword( $title );
        }

        // Analyser la densité des mots-clés
        return $this->analyze_keyword_density( $content, $title, $excerpt, $focus_keyword );
    }

    /**
     * Analyse les mots-clés d'une URL
     *
     * @param string $url URL à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_url( $url ) {
        // Récupérer le contenu de la page
        $response = wp_remote_get( $url );
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }

        $html = wp_remote_retrieve_body( $response );
        if ( empty( $html ) ) {
            return array(
                'status' => 'error',
                'message' => __( 'Impossible de récupérer le contenu de la page.', 'smartseo-ai' ),
            );
        }

        // Créer un objet DOMDocument
        $doc = new DOMDocument();
        @$doc->loadHTML( mb_convert_encoding( $html, 'HTML-ENTITIES', 'UTF-8' ) );
        $xpath = new DOMXPath( $doc );

        // Récupérer le titre
        $title_nodes = $xpath->query( '//title' );
        $title = '';
        if ( $title_nodes->length > 0 ) {
            $title = $title_nodes->item( 0 )->nodeValue;
        }

        // Récupérer la meta description
        $meta_description = '';
        $meta_nodes = $xpath->query( '//meta[@name="description"]' );
        if ( $meta_nodes->length > 0 ) {
            $meta_description = $meta_nodes->item( 0 )->getAttribute( 'content' );
        }

        // Récupérer le contenu principal
        $content = '';
        $body_nodes = $xpath->query( '//body' );
        if ( $body_nodes->length > 0 ) {
            $content = $body_nodes->item( 0 )->nodeValue;
        }

        // Extraire le mot-clé principal du titre
        $focus_keyword = $this->extract_focus_keyword( $title );

        // Analyser la densité des mots-clés
        return $this->analyze_keyword_density( $content, $title, $meta_description, $focus_keyword );
    }

    /**
     * Analyse la densité des mots-clés dans le contenu
     *
     * @param string $content       Contenu à analyser.
     * @param string $title         Titre de la page.
     * @param string $excerpt       Extrait ou meta description.
     * @param string $focus_keyword Mot-clé principal.
     * @return array Résultats de l'analyse.
     */
    private function analyze_keyword_density( $content, $title, $excerpt, $focus_keyword ) {
        $results = array(
            'status' => 'success',
            'focus_keyword' => $focus_keyword,
            'keyword_density' => 0,
            'keyword_count' => 0,
            'total_words' => 0,
            'top_keywords' => array(),
            'issues' => array(),
            'recommendations' => array(),
            'score' => 0,
        );

        // Nettoyer le contenu
        $clean_content = $this->clean_content( $content );
        
        // Compter le nombre total de mots
        $words = preg_split( '/\s+/', $clean_content );
        $total_words = count( $words );
        $results['total_words'] = $total_words;

        // Si aucun mot-clé n'est défini, on ne peut pas calculer la densité
        if ( empty( $focus_keyword ) ) {
            $results['issues'][] = __( 'Aucun mot-clé principal n\'a été défini.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Définissez un mot-clé principal pour l\'analyse.', 'smartseo-ai' );
            
            // Extraire les mots-clés les plus fréquents
            $results['top_keywords'] = $this->extract_top_keywords( $clean_content, 5 );
            
            // Suggérer un mot-clé principal
            if ( ! empty( $results['top_keywords'] ) ) {
                $suggested_keyword = key( $results['top_keywords'] );
                $results['recommendations'][] = sprintf(
                    __( 'Vous pourriez utiliser "%s" comme mot-clé principal.', 'smartseo-ai' ),
                    $suggested_keyword
                );
            }
            
            return $results;
        }

        // Compter les occurrences du mot-clé principal
        $keyword_count = substr_count( strtolower( $clean_content ), strtolower( $focus_keyword ) );
        $results['keyword_count'] = $keyword_count;

        // Calculer la densité des mots-clés
        if ( $total_words > 0 ) {
            $keyword_density = ( $keyword_count / $total_words ) * 100;
            $results['keyword_density'] = round( $keyword_density, 2 );
        }

        // Vérifier si le mot-clé est présent dans le titre
        $keyword_in_title = strpos( strtolower( $title ), strtolower( $focus_keyword ) ) !== false;
        $results['keyword_in_title'] = $keyword_in_title;

        // Vérifier si le mot-clé est présent dans l'extrait ou la meta description
        $keyword_in_excerpt = strpos( strtolower( $excerpt ), strtolower( $focus_keyword ) ) !== false;
        $results['keyword_in_excerpt'] = $keyword_in_excerpt;

        // Extraire les mots-clés les plus fréquents
        $results['top_keywords'] = $this->extract_top_keywords( $clean_content, 5 );

        // Analyser les résultats
        if ( $results['keyword_density'] < 0.5 ) {
            $results['issues'][] = __( 'La densité du mot-clé principal est trop faible.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Augmentez la fréquence du mot-clé principal dans le contenu.', 'smartseo-ai' );
        } elseif ( $results['keyword_density'] > 3 ) {
            $results['issues'][] = __( 'La densité du mot-clé principal est trop élevée (bourrage de mots-clés).', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Réduisez la fréquence du mot-clé principal pour éviter le bourrage de mots-clés.', 'smartseo-ai' );
        }

        if ( ! $keyword_in_title ) {
            $results['issues'][] = __( 'Le mot-clé principal n\'est pas présent dans le titre.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Incluez le mot-clé principal dans le titre.', 'smartseo-ai' );
        }

        if ( ! $keyword_in_excerpt && ! empty( $excerpt ) ) {
            $results['issues'][] = __( 'Le mot-clé principal n\'est pas présent dans l\'extrait ou la meta description.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Incluez le mot-clé principal dans l\'extrait ou la meta description.', 'smartseo-ai' );
        }

        // Calculer le score
        $score = 100;

        // Pénalité pour une densité trop faible ou trop élevée
        if ( $results['keyword_density'] < 0.5 ) {
            $score -= 30;
        } elseif ( $results['keyword_density'] > 3 ) {
            $score -= 40;
        }

        // Pénalité si le mot-clé n'est pas dans le titre
        if ( ! $keyword_in_title ) {
            $score -= 20;
        }

        // Pénalité si le mot-clé n'est pas dans l'extrait
        if ( ! $keyword_in_excerpt && ! empty( $excerpt ) ) {
            $score -= 10;
        }

        $results['score'] = max( 0, $score );

        return $results;
    }

    /**
     * Nettoie le contenu pour l'analyse
     *
     * @param string $content Contenu à nettoyer.
     * @return string Contenu nettoyé.
     */
    private function clean_content( $content ) {
        // Supprimer les balises HTML
        $content = strip_tags( $content );
        
        // Supprimer les caractères spéciaux
        $content = preg_replace( '/[^\p{L}\p{N}\s]/u', ' ', $content );
        
        // Supprimer les espaces multiples
        $content = preg_replace( '/\s+/', ' ', $content );
        
        // Convertir en minuscules
        $content = strtolower( $content );
        
        return trim( $content );
    }

    /**
     * Extrait les mots-clés les plus fréquents du contenu
     *
     * @param string $content Contenu à analyser.
     * @param int    $limit   Nombre de mots-clés à extraire.
     * @return array Mots-clés les plus fréquents avec leur nombre d'occurrences.
     */
    private function extract_top_keywords( $content, $limit = 5 ) {
        // Diviser le contenu en mots
        $words = preg_split( '/\s+/', $content );
        
        // Filtrer les mots courts et les mots vides
        $stop_words = array( 'le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'de', 'du', 'a', 'au', 'aux', 'en', 'par', 'pour', 'sur', 'the', 'a', 'an', 'and', 'or', 'of', 'to', 'in', 'on', 'by', 'for', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'but', 'at', 'this', 'that', 'these', 'those', 'with', 'from', 'as', 'not', 'no', 'yes', 'all', 'any', 'each', 'few', 'many', 'more', 'most', 'other', 'some', 'such', 'than', 'too', 'very', 'can', 'will', 'just', 'should', 'now' );
        $filtered_words = array_filter( $words, function( $word ) use ( $stop_words ) {
            return strlen( $word ) > 3 && ! in_array( $word, $stop_words, true );
        } );
        
        // Compter les occurrences de chaque mot
        $word_counts = array_count_values( $filtered_words );
        
        // Trier par nombre d'occurrences (décroissant)
        arsort( $word_counts );
        
        // Limiter le nombre de résultats
        return array_slice( $word_counts, 0, $limit, true );
    }

    /**
     * Extrait un mot-clé principal du titre
     *
     * @param string $title Titre à analyser.
     * @return string Mot-clé principal.
     */
    private function extract_focus_keyword( $title ) {
        // Nettoyer le titre
        $clean_title = $this->clean_content( $title );
        
        // Extraire les mots-clés les plus fréquents
        $top_keywords = $this->extract_top_keywords( $clean_title, 1 );
        
        // Retourner le premier mot-clé
        if ( ! empty( $top_keywords ) ) {
            return key( $top_keywords );
        }
        
        return '';
    }
}
