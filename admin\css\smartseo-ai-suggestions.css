/**
 * Styles pour les suggestions SEO basées sur l'IA
 */

/* Conteneur principal */
.smartseo-ai-suggestions-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.smartseo-ai-suggestions-form {
    flex: 1;
    min-width: 300px;
}

.smartseo-ai-suggestions-results {
    flex: 2;
    min-width: 500px;
}

/* Cartes */
.smartseo-ai-card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

/* Formulaires */
.smartseo-ai-form-group {
    margin-bottom: 15px;
}

.smartseo-ai-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.smartseo-ai-form-actions {
    margin-top: 20px;
}

.smartseo-ai-form-actions .dashicons {
    margin-right: 5px;
    vertical-align: middle;
}

/* Chargement */
.smartseo-ai-suggestions-loading,
.smartseo-ai-metabox-loading {
    text-align: center;
    padding: 30px 0;
}

.smartseo-ai-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 3px solid rgba(0, 115, 170, 0.2);
    border-radius: 50%;
    border-top-color: #0073aa;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* En-tête des suggestions */
.smartseo-ai-suggestions-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.smartseo-ai-suggestions-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

.smartseo-ai-suggestions-actions .dashicons {
    margin-right: 5px;
    vertical-align: middle;
}

/* Accordéons */
.smartseo-ai-accordion {
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.smartseo-ai-accordion-header {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f9f9f9;
    cursor: pointer;
    position: relative;
}

.smartseo-ai-accordion-header h3 {
    margin: 0;
    flex-grow: 1;
    font-size: 16px;
    margin-left: 10px;
}

.smartseo-ai-accordion-toggle {
    margin-left: 10px;
    transition: transform 0.2s;
}

.smartseo-ai-accordion-header.active .smartseo-ai-accordion-toggle {
    transform: rotate(180deg);
}

.smartseo-ai-accordion-content {
    display: none;
    padding: 20px;
    background: #fff;
    border-top: 1px solid #eee;
}

/* Éléments de suggestion */
.smartseo-ai-suggestion-item {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.smartseo-ai-suggestion-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.smartseo-ai-suggestion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.smartseo-ai-suggestion-content {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
}

/* Mots-clés */
.smartseo-ai-keyword {
    font-weight: bold;
    color: #0073aa;
    font-size: 16px;
}

/* Méta descriptions */
.smartseo-ai-meta-description {
    font-style: italic;
    margin-bottom: 10px;
}

.smartseo-ai-meta-count {
    color: #666;
    font-size: 13px;
}

.smartseo-ai-meta-keyword-included {
    color: #46b450;
    margin-top: 5px;
}

.smartseo-ai-meta-keyword-missing {
    color: #dc3232;
    margin-top: 5px;
}

/* Titres */
.smartseo-ai-heading {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 10px;
}

.smartseo-ai-heading-level {
    background: #0073aa;
    color: #fff;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.smartseo-ai-heading-original,
.smartseo-ai-heading-improved,
.smartseo-ai-heading-reason,
.smartseo-ai-heading-placement {
    margin-bottom: 5px;
}

/* Liens */
.smartseo-ai-link-target {
    font-weight: bold;
    color: #0073aa;
}

.smartseo-ai-link-anchor,
.smartseo-ai-link-context,
.smartseo-ai-link-reason {
    margin-bottom: 5px;
}

/* Images */
.smartseo-ai-image-preview {
    margin-right: 15px;
}

.smartseo-ai-alt-suggestion,
.smartseo-ai-alt-reason {
    margin-bottom: 5px;
}

/* Lisibilité */
.smartseo-ai-readability-score {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.smartseo-ai-score-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-weight: bold;
    font-size: 20px;
    margin-right: 15px;
}

.smartseo-ai-score-good {
    background: #46b450;
}

.smartseo-ai-score-average {
    background: #ffb900;
}

.smartseo-ai-score-poor {
    background: #dc3232;
}

.smartseo-ai-readability-level {
    font-size: 16px;
}

.smartseo-ai-issue-type,
.smartseo-ai-improvement-type,
.smartseo-ai-opportunity-type {
    font-weight: bold;
    color: #0073aa;
}

.smartseo-ai-issue-description,
.smartseo-ai-improvement-description,
.smartseo-ai-opportunity-description {
    margin-bottom: 10px;
}

.smartseo-ai-issue-example,
.smartseo-ai-improvement-example,
.smartseo-ai-issue-location,
.smartseo-ai-opportunity-location {
    margin-bottom: 5px;
    font-style: italic;
}

.smartseo-ai-issue-suggestion,
.smartseo-ai-improvement-suggestion,
.smartseo-ai-opportunity-suggestion {
    color: #46b450;
}

/* Rich Snippets */
.smartseo-ai-content-type {
    font-weight: bold;
    margin-bottom: 15px;
}

.smartseo-ai-schema-type {
    font-weight: bold;
    color: #0073aa;
}

.smartseo-ai-schema-relevance,
.smartseo-ai-schema-description,
.smartseo-ai-schema-implementation {
    margin-bottom: 5px;
}

.smartseo-ai-schema-json {
    background: #23282d;
    color: #fff;
    padding: 15px;
    border-radius: 4px;
    overflow: auto;
    max-height: 300px;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.5;
}

/* Boutons */
.smartseo-ai-apply-suggestion,
.smartseo-ai-copy-suggestion {
    white-space: nowrap;
}

.smartseo-ai-apply-suggestion .dashicons,
.smartseo-ai-copy-suggestion .dashicons {
    margin-right: 3px;
    vertical-align: middle;
}

.smartseo-ai-loading {
    position: relative;
    padding-left: 25px;
}

.smartseo-ai-loading:before {
    content: '';
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top-color: #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.smartseo-ai-success {
    background-color: #46b450 !important;
    border-color: #46b450 !important;
    color: #fff !important;
}

/* Meta box */
.smartseo-ai-metabox-container {
    padding: 0;
}

.smartseo-ai-metabox-header {
    margin-bottom: 15px;
}

.smartseo-ai-metabox-header h2 {
    margin-top: 0;
}

.smartseo-ai-metabox-actions {
    margin-bottom: 15px;
}

.smartseo-ai-metabox-actions .button {
    margin-right: 10px;
}

.smartseo-ai-metabox-summary {
    margin-top: 15px;
}

.smartseo-ai-metabox-summary h3 {
    margin-top: 0;
}

.smartseo-ai-metabox-summary-list {
    margin: 0 0 15px 0;
    padding: 0;
    list-style: none;
}

.smartseo-ai-metabox-summary-list li {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.smartseo-ai-metabox-summary-list li:before {
    content: '→';
    position: absolute;
    left: 0;
    top: 0;
    color: #0073aa;
}

/* Gutenberg */
.smartseo-ai-gutenberg-panel {
    padding: 0;
}

.smartseo-ai-suggestions-list {
    margin: 15px 0;
    padding: 0;
    list-style: none;
}

.smartseo-ai-suggestions-list li {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.smartseo-ai-suggestions-list li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.smartseo-ai-suggestions-list button {
    margin-top: 5px;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .smartseo-ai-suggestion-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .smartseo-ai-suggestion-header button {
        margin-top: 10px;
    }
    
    .smartseo-ai-readability-score {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .smartseo-ai-score-circle {
        margin-bottom: 10px;
    }
}
