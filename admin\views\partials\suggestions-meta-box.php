<?php
/**
 * Template pour la meta box des suggestions SEO
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Récupérer l'ID de l'article
$post_id = $post->ID;
?>

<div class="smartseo-ai-metabox-container">
    <div class="smartseo-ai-metabox-header">
        <h2><?php esc_html_e( 'Suggestions SEO basées sur l\'IA', 'smartseo-ai' ); ?></h2>
        <p><?php esc_html_e( 'Obtenez des suggestions d\'optimisation SEO personnalisées pour votre contenu.', 'smartseo-ai' ); ?></p>
    </div>
    
    <div class="smartseo-ai-metabox-actions">
        <button type="button" id="smartseo-ai-analyze-content" class="button button-primary">
            <span class="dashicons dashicons-superhero"></span>
            <?php esc_html_e( 'Analyser le contenu', 'smartseo-ai' ); ?>
        </button>
        
        <a href="<?php echo esc_url( admin_url( 'admin.php?page=smartseo-ai-suggestions&post_id=' . $post_id ) ); ?>" class="button button-secondary" target="_blank">
            <span class="dashicons dashicons-external"></span>
            <?php esc_html_e( 'Voir toutes les suggestions', 'smartseo-ai' ); ?>
        </a>
    </div>
    
    <div class="smartseo-ai-metabox-results" style="display: none;">
        <div class="smartseo-ai-metabox-loading">
            <div class="smartseo-ai-spinner"></div>
            <p><?php esc_html_e( 'Analyse en cours...', 'smartseo-ai' ); ?></p>
        </div>
        
        <div class="smartseo-ai-metabox-content" style="display: none;">
            <!-- Le contenu sera inséré ici via JavaScript -->
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Analyser le contenu
    $('#smartseo-ai-analyze-content').on('click', function() {
        var $button = $(this);
        var postId = '<?php echo esc_js( $post_id ); ?>';
        var content = '';
        
        // Récupérer le contenu de l'éditeur
        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('content')) {
            content = tinyMCE.get('content').getContent();
        } else {
            content = $('#content').val();
        }
        
        // Afficher la section des résultats
        $('.smartseo-ai-metabox-results').show();
        $('.smartseo-ai-metabox-loading').show();
        $('.smartseo-ai-metabox-content').hide();
        
        // Désactiver le bouton
        $button.prop('disabled', true).addClass('smartseo-ai-loading');
        
        // Envoyer la requête AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'smartseo_ai_get_suggestions',
                post_id: postId,
                content: content,
                nonce: '<?php echo wp_create_nonce( 'smartseo_ai_nonce' ); ?>'
            },
            success: function(response) {
                if (response.success) {
                    renderMetaboxSuggestions(response.data.suggestions, postId);
                } else {
                    alert(response.data.message || '<?php esc_attr_e( 'Une erreur s\'est produite lors de la génération des suggestions.', 'smartseo-ai' ); ?>');
                    $('.smartseo-ai-metabox-results').hide();
                }
            },
            error: function() {
                alert('<?php esc_attr_e( 'Une erreur s\'est produite lors de la communication avec le serveur.', 'smartseo-ai' ); ?>');
                $('.smartseo-ai-metabox-results').hide();
            },
            complete: function() {
                $('.smartseo-ai-metabox-loading').hide();
                $button.prop('disabled', false).removeClass('smartseo-ai-loading');
            }
        });
    });
    
    // Fonction pour afficher les suggestions dans la meta box
    function renderMetaboxSuggestions(suggestions, postId) {
        var html = '<div class="smartseo-ai-metabox-summary">';
        
        // Afficher un résumé des suggestions
        html += '<h3><?php esc_html_e( 'Résumé des suggestions', 'smartseo-ai' ); ?></h3>';
        html += '<ul class="smartseo-ai-metabox-summary-list">';
        
        // Mots-clés
        if (suggestions.keywords && suggestions.keywords.primary_keyword) {
            html += '<li><strong><?php esc_html_e( 'Mot-clé principal suggéré', 'smartseo-ai' ); ?>:</strong> ' + suggestions.keywords.primary_keyword.keyword + '</li>';
        }
        
        // Méta description
        if (suggestions.meta && suggestions.meta.meta_descriptions && suggestions.meta.meta_descriptions.length > 0) {
            html += '<li><strong><?php esc_html_e( 'Méta descriptions', 'smartseo-ai' ); ?>:</strong> ' + suggestions.meta.meta_descriptions.length + ' <?php esc_html_e( 'suggestions', 'smartseo-ai' ); ?></li>';
        }
        
        // Titres
        if (suggestions.headings) {
            var headingCount = 0;
            if (suggestions.headings.title_suggestion) headingCount++;
            if (suggestions.headings.existing_headings_improvements) headingCount += suggestions.headings.existing_headings_improvements.length;
            if (suggestions.headings.new_headings_suggestions) headingCount += suggestions.headings.new_headings_suggestions.length;
            
            if (headingCount > 0) {
                html += '<li><strong><?php esc_html_e( 'Titres et sous-titres', 'smartseo-ai' ); ?>:</strong> ' + headingCount + ' <?php esc_html_e( 'suggestions', 'smartseo-ai' ); ?></li>';
            }
        }
        
        // Liens internes
        if (suggestions.internal_links && suggestions.internal_links.link_suggestions) {
            html += '<li><strong><?php esc_html_e( 'Liens internes', 'smartseo-ai' ); ?>:</strong> ' + suggestions.internal_links.link_suggestions.length + ' <?php esc_html_e( 'suggestions', 'smartseo-ai' ); ?></li>';
        }
        
        // Images
        if (suggestions.image_alt && suggestions.image_alt.alt_suggestions) {
            html += '<li><strong><?php esc_html_e( 'Balises alt', 'smartseo-ai' ); ?>:</strong> ' + suggestions.image_alt.alt_suggestions.length + ' <?php esc_html_e( 'suggestions', 'smartseo-ai' ); ?></li>';
        }
        
        // Lisibilité
        if (suggestions.readability && suggestions.readability.readability_score) {
            var readabilityClass = '';
            if (suggestions.readability.readability_score >= 80) {
                readabilityClass = 'good';
            } else if (suggestions.readability.readability_score >= 60) {
                readabilityClass = 'average';
            } else {
                readabilityClass = 'poor';
            }
            
            html += '<li><strong><?php esc_html_e( 'Score de lisibilité', 'smartseo-ai' ); ?>:</strong> <span class="smartseo-ai-score-' + readabilityClass + '">' + suggestions.readability.readability_score + '</span></li>';
        }
        
        // Rich snippets
        if (suggestions.rich_snippets && suggestions.rich_snippets.recommended_schemas) {
            html += '<li><strong><?php esc_html_e( 'Rich snippets', 'smartseo-ai' ); ?>:</strong> ' + suggestions.rich_snippets.recommended_schemas.length + ' <?php esc_html_e( 'schémas recommandés', 'smartseo-ai' ); ?></li>';
        }
        
        html += '</ul>';
        
        // Lien vers la page complète des suggestions
        html += '<p><a href="<?php echo esc_url( admin_url( 'admin.php?page=smartseo-ai-suggestions&post_id=' ) ); ?>' + postId + '" class="button button-primary" target="_blank">';
        html += '<span class="dashicons dashicons-visibility"></span> <?php esc_html_e( 'Voir toutes les suggestions', 'smartseo-ai' ); ?>';
        html += '</a></p>';
        
        html += '</div>';
        
        // Afficher les suggestions
        $('.smartseo-ai-metabox-content').html(html).show();
    }
});
</script>
