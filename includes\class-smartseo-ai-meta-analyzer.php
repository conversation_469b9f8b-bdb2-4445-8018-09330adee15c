<?php
/**
 * Classe pour analyser les balises meta
 *
 * @package SmartSEO_AI
 */

// Si ce fichier est appelé directement, on sort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe qui analyse les balises title et meta description
 */
class SmartSEO_AI_Meta_Analyzer {

    /**
     * Constructeur
     */
    public function __construct() {
        // Rien à initialiser pour l'instant
    }

    /**
     * Analyse les balises meta d'un article
     *
     * @param int $post_id ID de l'article à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_post( $post_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return array(
                'status' => 'error',
                'message' => __( 'Article introuvable.', 'smartseo-ai' ),
            );
        }

        // Récupérer les données
        $title = get_post_meta( $post_id, '_yoast_wpseo_title', true );
        if ( empty( $title ) ) {
            $title = get_the_title( $post_id );
        }

        $meta_description = get_post_meta( $post_id, '_yoast_wpseo_metadesc', true );
        if ( empty( $meta_description ) ) {
            $meta_description = get_post_meta( $post_id, 'smartseo_ai_meta_description', true );
        }

        // Analyser le titre
        $title_analysis = $this->analyze_title( $title );

        // Analyser la meta description
        $description_analysis = $this->analyze_meta_description( $meta_description );

        return array(
            'status' => 'success',
            'title' => array(
                'content' => $title,
                'length' => mb_strlen( $title ),
                'analysis' => $title_analysis,
            ),
            'meta_description' => array(
                'content' => $meta_description,
                'length' => mb_strlen( $meta_description ),
                'analysis' => $description_analysis,
            ),
        );
    }

    /**
     * Analyse les balises meta d'une URL
     *
     * @param string $url URL à analyser.
     * @return array Résultats de l'analyse.
     */
    public function analyze_url( $url ) {
        // Récupérer le contenu de la page
        $response = wp_remote_get( $url );
        if ( is_wp_error( $response ) ) {
            return array(
                'status' => 'error',
                'message' => $response->get_error_message(),
            );
        }

        $html = wp_remote_retrieve_body( $response );
        if ( empty( $html ) ) {
            return array(
                'status' => 'error',
                'message' => __( 'Impossible de récupérer le contenu de la page.', 'smartseo-ai' ),
            );
        }

        // Créer un objet DOMDocument
        $doc = new DOMDocument();
        @$doc->loadHTML( mb_convert_encoding( $html, 'HTML-ENTITIES', 'UTF-8' ) );
        $xpath = new DOMXPath( $doc );

        // Récupérer le titre
        $title_nodes = $xpath->query( '//title' );
        $title = '';
        if ( $title_nodes->length > 0 ) {
            $title = $title_nodes->item( 0 )->nodeValue;
        }

        // Récupérer la meta description
        $meta_description = '';
        $meta_nodes = $xpath->query( '//meta[@name="description"]' );
        if ( $meta_nodes->length > 0 ) {
            $meta_description = $meta_nodes->item( 0 )->getAttribute( 'content' );
        }

        // Analyser le titre
        $title_analysis = $this->analyze_title( $title );

        // Analyser la meta description
        $description_analysis = $this->analyze_meta_description( $meta_description );

        return array(
            'status' => 'success',
            'title' => array(
                'content' => $title,
                'length' => mb_strlen( $title ),
                'analysis' => $title_analysis,
            ),
            'meta_description' => array(
                'content' => $meta_description,
                'length' => mb_strlen( $meta_description ),
                'analysis' => $description_analysis,
            ),
        );
    }

    /**
     * Analyse la balise title
     *
     * @param string $title Contenu de la balise title.
     * @return array Résultats de l'analyse.
     */
    private function analyze_title( $title ) {
        $results = array(
            'issues' => array(),
            'recommendations' => array(),
            'score' => 0,
        );

        // Vérifier si le titre existe
        if ( empty( $title ) ) {
            $results['issues'][] = __( 'La balise title est manquante.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Ajoutez une balise title pertinente.', 'smartseo-ai' );
            return $results;
        }

        // Vérifier la longueur du titre (idéalement entre 50 et 60 caractères)
        $title_length = mb_strlen( $title );
        if ( $title_length < 30 ) {
            $results['issues'][] = __( 'La balise title est trop courte.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Allongez votre titre pour qu\'il contienne entre 50 et 60 caractères.', 'smartseo-ai' );
        } elseif ( $title_length > 60 ) {
            $results['issues'][] = __( 'La balise title est trop longue.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Raccourcissez votre titre pour qu\'il contienne entre 50 et 60 caractères.', 'smartseo-ai' );
        }

        // Calculer le score
        if ( $title_length >= 50 && $title_length <= 60 ) {
            $results['score'] = 100;
        } elseif ( $title_length >= 40 && $title_length <= 70 ) {
            $results['score'] = 80;
        } elseif ( $title_length >= 30 && $title_length <= 80 ) {
            $results['score'] = 60;
        } else {
            $results['score'] = 40;
        }

        return $results;
    }

    /**
     * Analyse la balise meta description
     *
     * @param string $description Contenu de la balise meta description.
     * @return array Résultats de l'analyse.
     */
    private function analyze_meta_description( $description ) {
        $results = array(
            'issues' => array(),
            'recommendations' => array(),
            'score' => 0,
        );

        // Vérifier si la meta description existe
        if ( empty( $description ) ) {
            $results['issues'][] = __( 'La balise meta description est manquante.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Ajoutez une meta description pertinente.', 'smartseo-ai' );
            return $results;
        }

        // Vérifier la longueur de la meta description (idéalement entre 120 et 155 caractères)
        $description_length = mb_strlen( $description );
        if ( $description_length < 100 ) {
            $results['issues'][] = __( 'La meta description est trop courte.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Allongez votre meta description pour qu\'elle contienne entre 120 et 155 caractères.', 'smartseo-ai' );
        } elseif ( $description_length > 160 ) {
            $results['issues'][] = __( 'La meta description est trop longue.', 'smartseo-ai' );
            $results['recommendations'][] = __( 'Raccourcissez votre meta description pour qu\'elle contienne entre 120 et 155 caractères.', 'smartseo-ai' );
        }

        // Calculer le score
        if ( $description_length >= 120 && $description_length <= 155 ) {
            $results['score'] = 100;
        } elseif ( $description_length >= 100 && $description_length <= 170 ) {
            $results['score'] = 80;
        } elseif ( $description_length >= 80 && $description_length <= 200 ) {
            $results['score'] = 60;
        } else {
            $results['score'] = 40;
        }

        return $results;
    }
}
